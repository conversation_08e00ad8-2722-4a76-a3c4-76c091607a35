/**
 * 创业论坛API类型声明
 */

/**
 * 论坛项目类型
 */
export interface ForumProject {
  id?: number;
  title: string;
  content: string;
  author: string;
  type?: number;
  createTime?: string;
  updateTime?: string;
}

/**
 * 论坛查询参数
 */
export interface ForumQueryParams {
  type?: number;
  page?: number;
  limit?: number;
}

/**
 * 获取论坛列表
 * @param params 查询参数
 * @returns 论坛列表数据
 */
export function fetchForumList(params?: ForumQueryParams): Promise<any>;

/**
 * 获取论坛详情
 * @param id 论坛ID
 * @returns 论坛详情
 */
export function fetchForumDetail(id: number): Promise<any>;

/**
 * 发布用户项目
 * @param data 项目数据
 * @returns 发布结果
 */
export function publishUserProject(data: ForumProject): Promise<any>; 