<template>
  <div class="article-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="政策标题">
            <el-input v-model="searchForm.title" placeholder="请输入政策标题" clearable />
          </el-form-item>
          <el-form-item label="政策类型">
            <el-select v-model="searchForm.articleType" placeholder="请选择政策类型" clearable style="width: 220px">
              <el-option
                v-for="item in articleTypes"
                :key="item.articleType || item.typeName || item.id"
                :label="item.articleType || item.typeName"
                :value="item.articleType || item.typeName"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-container">
          <el-button type="primary" @click="handleAdd">新增政策</el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="articleList"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="title" label="政策标题" min-width="120" show-overflow-tooltip />
        <el-table-column prop="articleType" label="政策类型" width="100" />
        <el-table-column prop="summary" label="简介" min-width="120" show-overflow-tooltip />
        <el-table-column prop="picture" label="封面图片" width="100">
          <template #default="scope">
            <img v-if="scope.row.picture" :src="scope.row.picture" alt="封面" style="width:60px;height:60px;object-fit:cover;border-radius:4px;" />
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="publishDate" label="发布时间" width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.publishDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="lastClickTime" label="最后点击时间" width="120">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastClickTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="clickNum" label="阅读量" width="80" />
        <el-table-column prop="voteCount" label="评论数" width="80" />
        <el-table-column prop="thumbsUpNum" label="点赞" width="70" />
        <el-table-column prop="thumbsDownNum" label="点踩" width="70" />
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formType === 'add' ? '新增政策' : '编辑政策'"
      width="800px"
      @close="closeDialog"
    >
      <el-form
        ref="articleFormRef"
        :model="articleForm"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="政策标题" prop="title">
          <el-input v-model="articleForm.title" placeholder="请输入政策标题" />
        </el-form-item>
        <el-form-item label="政策类型" prop="articleType">
          <el-select v-model="articleForm.articleType" placeholder="请选择政策类型" style="width: 220px">
            <el-option
              v-for="item in articleTypes"
              :key="item.articleType || item.typeName || item.id"
              :label="item.articleType || item.typeName"
              :value="item.articleType || item.typeName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="封面图片" prop="picture">
          <el-upload
            class="article-cover-uploader"
            action="/api/uploads/file"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload"
          >
            <img v-if="articleForm.picture" :src="articleForm.picture" class="article-cover" />
            <el-icon v-else class="article-cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="政策简介" prop="summary">
          <el-input v-model="articleForm.summary" type="textarea" :rows="3" placeholder="请输入政策简介" />
        </el-form-item>
        <el-form-item label="政策内容" prop="content">
          <div class="editor-container">
            <!-- 这里可以集成富文本编辑器，如 Quill, TinyMCE, CKEditor 等 -->
            <el-input v-model="articleForm.content" type="textarea" :rows="10" placeholder="请输入政策内容" />
          </div>
        </el-form-item>
        <el-form-item label="发布时间" prop="publishDate">
          <el-date-picker
            v-model="articleForm.publishDate"
            type="datetime"
            placeholder="选择发布时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="是否置顶" prop="isTop">
          <el-switch v-model="articleForm.isTop" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="政策详情"
      width="800px"
    >
      <div v-if="currentArticle" class="article-detail">
        <h2 class="article-title" style="text-align:center;font-weight:600;font-size:22px;margin-bottom:10px;">{{ currentArticle.title }}</h2>
        <div class="article-meta" style="display:flex;justify-content:center;align-items:center;font-size:13px;color:#888;background:#f5f7fa;padding:8px 0;border-radius:4px;margin-bottom:18px;gap:16px;">
          <span>类型：{{ currentArticle.articleType }}</span>
          <span>|</span>
          <span>发布时间：{{ formatDateTime(currentArticle.publishDate) }}</span>
          <span>|</span>
          <span>阅读量：{{ currentArticle.clickNum }}</span>
          <span>|</span>
          <span>评论数：{{ currentArticle.voteCount }}</span>
          <span>|</span>
          <span>点赞：{{ currentArticle.thumbsUpNum }}</span>
          <span>|</span>
          <span>点踩：{{ currentArticle.thumbsDownNum }}</span>
        </div>
        <div v-if="currentArticle.picture" class="article-cover-preview" style="display:flex;justify-content:center;margin:18px 0;">
          <img :src="currentArticle.picture" alt="政策封面" style="max-width:150px;max-height:150px;object-fit:cover;border-radius:10px;box-shadow:0 2px 8px rgba(0,0,0,0.08);background:#fff;padding:6px;" />
        </div>
        <div class="article-summary" style="margin:24px 0 12px 0;padding-bottom:8px;border-bottom:1px solid #ebeef5;">
          <h3 style="font-size:16px;font-weight:500;margin-bottom:6px;">政策简介：</h3>
          <p style="margin:0;color:#444;">{{ currentArticle.summary }}</p>
        </div>
        <div class="article-content" style="margin:18px 0 0 0;">
          <h3 style="font-size:16px;font-weight:500;margin-bottom:6px;">政策内容：</h3>
          <div v-html="currentArticle.content" style="color:#333;line-height:1.8;"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { listArticles, getArticle, addArticle, updateArticle, deleteArticle } from '@/api/article'
import { listArticleTypes } from '@/api/article'

// 加载状态
const tableLoading = ref(false)

// 表单引用，用于表单校验
const articleFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  title: '',
  articleType: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 政策列表数据
const articleList = ref([])

// 政策类型列表
const articleTypes = ref([])

// 对话框控制
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const formType = ref('add') // add: 新增, edit: 编辑

// 当前政策（用于详情查看）
const currentArticle = ref(null)

// 表单数据
const articleForm = reactive({
  id: undefined,
  title: '',
  articleType: '',
  picture: '',
  summary: '',
  voteCount: 0,
  publishDate: '',
  content: '',
  thumbsUpNum: 0,
  thumbsDownNum: 0,
  lastClickTime: '',
  clickNum: 0,
  createTime: '',
  updateTime: '',
  isTop: false
})

// 表单校验规则
const rules = {
  title: [
    { required: true, message: '请输入政策标题', trigger: 'blur' },
    { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
  ],
  articleType: [
    { required: true, message: '请选择政策类型', trigger: 'change' }
  ],
  summary: [
    { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入政策内容', trigger: 'blur' }
  ],
  publishDate: [
    { required: true, message: '请选择发布时间', trigger: 'change' }
  ]
}

// 初始化加载数据
onMounted(() => {
  fetchArticleList()
  fetchArticleTypes()
})

// 获取政策类型列表
const fetchArticleTypes = async () => {
  try {
    // 获取所有类型（无分页）
    const res = await listArticleTypes({ pageSize: 100 })
    articleTypes.value = res.list || res.records || []
  } catch (error) {
    console.error('获取政策类型列表失败:', error)
    ElMessage.error('获取政策类型列表失败')
  }
}

// 获取政策列表
const fetchArticleList = async () => {
  tableLoading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      title: searchForm.title,
      articleType: searchForm.articleType
    }
    const res = await listArticles(params)
    articleList.value = res.list || res.records || []
    pagination.total = res.total || (res.list ? res.list.length : 0)
  } catch (error) {
    console.error('获取政策列表失败:', error)
    ElMessage.error('获取政策列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 查询
const handleSearch = () => {
  pagination.current = 1 // 重置到第一页
  fetchArticleList()
}

// 重置搜索
const resetSearch = () => {
  // 重置搜索表单
  searchForm.title = ''
  searchForm.articleType = ''

  // 重新查询
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchArticleList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  fetchArticleList()
}

// 查看政策详情
const handleView = async (row) => {
  try {
    const res = await getArticle(row.id)
    currentArticle.value = res
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取政策详情失败:', error)
    ElMessage.error('获取政策详情失败')
  }
}

// 新增政策
const handleAdd = () => {
  formType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑政策
const handleEdit = async (row) => {
  formType.value = 'edit'
  resetForm()

  try {
    const res = await getArticle(row.id)
    Object.assign(articleForm, res)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取政策详情失败:', error)
    ElMessage.error('获取政策详情失败')
  }
}

// 删除政策
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除政策"${row.title}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteArticle(row.id)
      ElMessage.success('删除成功')
      // 如果删除的是当前页的最后一条数据且不是第一页，则跳转到上一页
      if (articleList.value.length === 1 && pagination.current > 1) {
        pagination.current--
      }
      fetchArticleList()
    } catch (error) {
      console.error('删除政策失败:', error)
      ElMessage.error('删除政策失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 重置表单
const resetForm = () => {
  if (articleFormRef.value) {
    articleFormRef.value.resetFields()
  }

  // 手动重置表单数据
  Object.assign(articleForm, {
    id: undefined,
    title: '',
    articleType: '',
    picture: '',
    summary: '',
    voteCount: 0,
    publishDate: '',
    content: '',
    thumbsUpNum: 0,
    thumbsDownNum: 0,
    lastClickTime: '',
    clickNum: 0,
    createTime: '',
    updateTime: '',
    isTop: false
  })
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!articleFormRef.value) return

  await articleFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formType.value === 'add') {
          // 新增
          await addArticle(articleForm)
          ElMessage.success('新增成功')
        } else {
          // 编辑
          await updateArticle(articleForm.id, articleForm)
          ElMessage.success('更新成功')
        }

        // 关闭对话框并刷新列表
        dialogVisible.value = false
        fetchArticleList()
      } catch (error) {
        console.error(formType.value === 'add' ? '新增政策失败:' : '更新政策失败:', error)
        ElMessage.error(formType.value === 'add' ? '新增政策失败' : '更新政策失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 封面图片上传成功回调
const handleCoverSuccess = (res, file) => {
  articleForm.picture = res.url || (res.data && res.data.url) || ''
}

// 上传前检查
const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('封面只能是图片格式!')
  }

  if (!isLt2M) {
    ElMessage.error('封面图片大小不能超过 2MB!')
  }

  return isImage && isLt2M
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'

  // 如果是字符串类型，先转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime

  // 如果转换后不是有效日期
  if (isNaN(date.getTime())) return dateTime

  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}
</script>

<style scoped>
.article-management-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.search-form {
  flex: 1;
}

.operation-container {
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.article-cover-uploader {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.article-cover-uploader:hover {
  border-color: #409eff;
}

.article-cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.article-cover {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

/* 政策详情样式 */
.article-detail {
  padding: 20px;
}

.article-title {
  text-align: center;
  margin-bottom: 20px;
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.article-meta span {
  margin-right: 20px;
  margin-bottom: 10px;
}

.article-cover-preview {
  margin: 20px 0;
  text-align: center;
}

.article-cover-preview img {
  max-width: 100%;
  max-height: 300px;
  object-fit: cover;
}

.article-summary,
.article-content {
  margin-top: 20px;
}

.article-summary h3,
.article-content h3 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}
</style>
