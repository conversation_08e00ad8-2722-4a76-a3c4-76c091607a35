import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import { recordHistory, getPageTitle } from '../utils/history'

import Home from '../views/Home.vue'
import ArticleInfoList from '../views/ArticleInfoList.vue'
import NewsList from '../views/NewsList.vue'
import RegionPolicyList from '../views/RegionPolicyList.vue'
import ForumList from '../views/ForumList.vue'
import Feedback from '../views/Feedback.vue'
import Profile from '../views/Profile.vue'
import ArticleDetail from '../views/ArticleDetail.vue'
import NewsDetail from '../views/NewsDetail.vue'
import RegionPolicyDetail from '../views/RegionPolicyDetail.vue'
import ForumDetail from '../views/ForumDetail.vue'
import Login from '../views/Login.vue'

export const routes: RouteRecordRaw[] = [
  { path: '/', component: Home, meta: { title: '首页' } },
  { path: '/policy', component: ArticleInfoList, meta: { title: '政策信息' } },
  { path: '/news', component: NewsList, meta: { title: '资讯信息' } },
  { path: '/region-policy', component: RegionPolicyList, meta: { title: '地区政策' } },
  { path: '/forum', component: ForumList, meta: { title: '创业论坛' } },
  { path: '/feedback', component: Feedback, meta: { title: '留言反馈' } },
  { path: '/profile', component: Profile, meta: { title: '个人中心' } },
  { path: '/login', component: Login, meta: { title: '登录/注册', hidden: true } },
  { path: '/policy/:id', component: ArticleDetail, meta: { hidden: true } },
  { path: '/news/:id', component: NewsDetail, meta: { hidden: true } },
  { path: '/region-policy/:id', component: RegionPolicyDetail, meta: { hidden: true } },
  { path: '/forum/:id', component: ForumDetail, meta: { hidden: true } },
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 进入需要登录的页面前检查是否已登录
router.beforeEach((to, from, next) => {
  // 设置页面标题
  const pageTitle = getPageTitle(to);
  document.title = pageTitle ? `${pageTitle} - 创业政策平台` : '创业政策平台';
  
  // 需要登录才能访问的页面路径
  const requiresAuth = ['/profile'];
  
  // 如果目标路径是登录页且已有token，直接跳转到首页
  if (to.path === '/login') {
    const token = localStorage.getItem('token');
    if (token) {
      console.log('已登录，自动跳转到首页');
      next({ path: '/' });
      return;
    }
    // 登录页不需要登录权限，直接允许访问
    console.log('前往登录页面');
    next();
    return;
  }
  
  // 检查是否需要登录权限
  if (requiresAuth.includes(to.path)) {
    // 检查是否已登录
    const token = localStorage.getItem('token');
    if (!token) {
      // 未登录，跳转到登录页，并携带重定向信息
      console.log('需要登录：跳转到登录页面');
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 登录后可以重定向回来
      });
      return;
    } else {
      // 已有token，允许访问
      console.log('有token，允许访问个人中心');
      next();
      return;
    }
  } else {
    // 不需要登录的页面，直接访问
    console.log('访问不需要登录的页面：', to.path);
    next();
  }
});

// 记录浏览历史
router.afterEach((to) => {
  // 不记录登录页和个人中心页面的历史
  if (to.path === '/login' || to.path === '/profile' || to.path === '/feedback') {
    return;
  }
  
  // 延迟记录，确保页面加载完成后获取到正确的名称和图片等数据
  setTimeout(() => {
    // 调用记录历史函数
    recordHistory(to, {
      name: document.title.split(' - ')[0], // 获取页面标题作为名称
    });
  }, 500);
});

export default router 