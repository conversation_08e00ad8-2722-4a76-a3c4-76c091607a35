/**
 * 浏览历史工具类
 * 用于记录用户浏览历史
 */
import { recordBrowseHistory } from '../api/history'

// 存储上一次记录的URL，避免重复记录
let lastRecordedPath = '';

/**
 * 获取表名和记录ID
 * @param {string} path 路由路径
 * @returns {Object|null} 表名和记录ID对象，无法解析则返回null
 */
function parsePathForHistory(path) {
  // 路径格式映射
  const pathMap = {
    '/policy': { tableName: 'policy_article', type: '政策信息' },
    '/news': { tableName: 'news', type: '资讯信息' },
    '/region-policy': { tableName: 'region_policy', type: '地区政策' },
    '/forum': { tableName: 'forum', type: '创业论坛' }
  };

  // 详情页路径解析
  if (path.startsWith('/policy/')) {
    const id = path.split('/').pop();
    return { tableName: 'policy_article', refid: id, type: '政策信息' };
  }
  
  if (path.startsWith('/news/')) {
    const id = path.split('/').pop();
    return { tableName: 'news', refid: id, type: '资讯信息' };
  }
  
  if (path.startsWith('/region-policy/')) {
    const id = path.split('/').pop();
    return { tableName: 'region_policy', refid: id, type: '地区政策' };
  }
  
  if (path.startsWith('/forum/')) {
    const id = path.split('/').pop();
    return { tableName: 'forum', refid: id, type: '创业论坛' };
  }
  
  // 列表页处理
  return pathMap[path] || null;
}

/**
 * 记录浏览历史
 * @param {Object} to 目标路由
 * @param {Object} store 状态管理工具（可选）
 */
export function recordHistory(to, additionalData = {}) {
  // 检查是否登录
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('用户未登录，不记录浏览历史');
    return;
  }
  
  // 避免重复记录同一个页面
  if (to.fullPath === lastRecordedPath) {
    console.log('避免重复记录同一页面:', to.fullPath);
    return;
  }
  
  // 解析路径获取表名和记录ID
  const pathInfo = parsePathForHistory(to.path);
  if (!pathInfo) {
    console.log('当前页面不需要记录浏览历史:', to.path);
    return;
  }
  
  // 构建历史记录数据
  const historyData = {
    tableName: pathInfo.tableName,
    type: pathInfo.type,
    ...additionalData
  };
  
  // 如果是详情页，添加ID
  if (pathInfo.refid) {
    historyData.refid = pathInfo.refid;
  }
  
  // 记录浏览历史
  console.log('记录浏览历史:', historyData);
  recordBrowseHistory(historyData)
    .then(res => {
      console.log('浏览历史记录成功:', res);
      // 更新最后记录的路径
      lastRecordedPath = to.fullPath;
    })
    .catch(err => {
      console.error('浏览历史记录失败:', err);
    });
}

/**
 * 获取页面标题
 * @param {Object} route 路由对象
 * @returns {string} 页面标题
 */
export function getPageTitle(route) {
  if (route.meta && route.meta.title) {
    return route.meta.title;
  }
  
  // 详情页标题处理
  if (route.path.includes('/policy/')) {
    return '政策详情';
  }
  if (route.path.includes('/news/')) {
    return '资讯详情';
  }
  if (route.path.includes('/region-policy/')) {
    return '地区政策详情';
  }
  if (route.path.includes('/forum/')) {
    return '论坛讨论详情';
  }
  
  return '创业政策平台';
} 