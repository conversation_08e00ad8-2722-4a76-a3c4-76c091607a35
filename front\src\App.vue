<template>
  <div class="app-root">
    <NavBar />
    <div class="main-content">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import NavBar from './components/NavBar.vue'
</script>

<style>
body {
  margin: 0;
  padding: 0;
}

.app-root {
  min-height: 100vh;
  background: #f5f6fa;
  width: 100%;
  overflow-x: hidden;
}

.main-content {
  width: 100%;
  max-width: 100%;
  margin: 20px auto 0 auto;
  padding: 0;
  min-height: 600px;
}
</style> 