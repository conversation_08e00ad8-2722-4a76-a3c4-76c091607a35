# 核心代码文档

## 1. 系统架构与配置

### 1.1 项目结构

项目采用前后端一体化管理架构，所有前端源码和依赖均放置于Spring Boot项目的`src/main/resources/static/`目录下。

```
entrepreneurship_policy_system/
├── src/
│   └── main/
│       ├── java/com/example/entrepreneurshippolicysystem/
│       │   ├── controller/     # 控制器
│       │   │   ├── admin/      # 管理后台接口
│       │   │   └── api/        # 用户端接口
│       │   ├── entity/         # 实体类
│       │   ├── service/        # 服务层
│       │   ├── mapper/         # 数据访问层
│       │   ├── dto/            # 数据传输对象
│       │   ├── config/         # 配置类
│       │   ├── security/       # 安全认证
│       │   └── util/           # 工具类
│       └── resources/
│           ├── application.yml
│           └── static/
│               ├── admin/   # 管理后台前端源码
│               └── front/   # 用户端前端源码
```

### 1.2 核心配置文件

**application.yml**
```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.example.entrepreneurshippolicysystem.entity
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: your-secret-key
  expiration: 86400000  # 24小时
  refresh-expiration: 604800000  # 7天

# 自定义配置
app:
  file-upload-path: /uploads/
  cors:
    allowed-origins: "*"
    allowed-methods: "*"
    allowed-headers: "*"
```

## 2. 安全认证模块

### 2.1 JWT工具类

```java
@Component
public class JwtTokenUtil {
    
    @Value("${jwt.secret}")
    private String secret;
    
    @Value("${jwt.expiration}")
    private Long expiration;
    
    // 生成JWT令牌
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        return doGenerateToken(claims, userDetails.getUsername());
    }
    
    // 验证JWT令牌
    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }
    
    // 从JWT令牌中获取用户名
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }
    
    // 检查令牌是否过期
    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }
    
    // 从令牌中获取过期日期
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }
    
    // 从令牌中获取声明
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }
    
    // 解析令牌获取所有声明
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser().setSigningKey(secret).parseClaimsJws(token).getBody();
    }
    
    // 生成令牌
    private String doGenerateToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();
    }
}
```

### 2.2 安全配置类

```java
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    
    @Autowired
    private UserDetailsService jwtUserDetailsService;
    
    @Autowired
    private JwtRequestFilter jwtRequestFilter;
    
    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) throws Exception {
        // 配置AuthenticationManager使用自定义的UserDetailsService
        auth.userDetailsService(jwtUserDetailsService).passwordEncoder(passwordEncoder());
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
    
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
            .csrf().disable()
            .authorizeRequests()
                .antMatchers("/api/auth/**").permitAll()
                .antMatchers("/admin/auth/**").permitAll()
                .antMatchers("/api/**").authenticated()
                .antMatchers("/admin/**").hasRole("ADMIN")
                .anyRequest().permitAll()
            .and()
            .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
            .and()
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
        
        // 添加JWT过滤器
        httpSecurity.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
    }
}
```

## 3. 政策信息模块

### 3.1 政策实体类

```java
@Data
@TableName("regional_policy")
public class RegionalPolicy implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    private String title;
    
    private String content;
    
    private String summary;
    
    private String region;
    
    @TableField("policy_level")
    private String policyLevel;
    
    @TableField("policy_type")
    private String policyType;
    
    @TableField("publish_time")
    private Date publishTime;
    
    private String source;
    
    @TableField("source_url")
    private String sourceUrl;
    
    private String attachment;
    
    @TableField("view_count")
    private Integer viewCount;
    
    private Integer status;
    
    @TableField("create_time")
    private Date createTime;
    
    @TableField("update_time")
    private Date updateTime;
}
```

### 3.2 政策服务接口

```java
public interface RegionalPolicyService extends IService<RegionalPolicy> {
    
    /**
     * 分页查询政策列表
     * @param page 分页参数
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    IPage<RegionalPolicy> pageList(Page<RegionalPolicy> page, QueryWrapper<RegionalPolicy> queryWrapper);
    
    /**
     * 获取政策详情并增加浏览次数
     * @param id 政策ID
     * @return 政策详情
     */
    RegionalPolicy getDetailAndIncrementViewCount(Long id);
    
    /**
     * 根据地区和政策类型查询政策
     * @param region 地区
     * @param policyType 政策类型
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<RegionalPolicy> getByRegionAndType(String region, String policyType, Page<RegionalPolicy> page);
    
    /**
     * 全文检索政策
     * @param keyword 关键词
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<RegionalPolicy> searchByKeyword(String keyword, Page<RegionalPolicy> page);
    
    /**
     * 为用户推荐政策
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐政策列表
     */
    List<RegionalPolicy> recommendForUser(Long userId, int limit);
}
```

### 3.3 政策服务实现类

```java
@Service
@CacheConfig(cacheNames = "policy")
public class RegionalPolicyServiceImpl extends ServiceImpl<RegionalPolicyMapper, RegionalPolicy> implements RegionalPolicyService {
    
    @Autowired
    private RegionalPolicyMapper policyMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private BrowseHistoryService browseHistoryService;
    
    @Override
    @Cacheable(key = "'page:' + #page.current + ':' + #page.size + ':' + #queryWrapper.toString()")
    public IPage<RegionalPolicy> pageList(Page<RegionalPolicy> page, QueryWrapper<RegionalPolicy> queryWrapper) {
        return policyMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    @Cacheable(key = "'detail:' + #id")
    public RegionalPolicy getDetailAndIncrementViewCount(Long id) {
        // 增加浏览次数
        policyMapper.incrementViewCount(id);
        
        // 获取政策详情
        return policyMapper.selectById(id);
    }
    
    @Override
    @Cacheable(key = "'region:' + #region + ':type:' + #policyType + ':page:' + #page.current")
    public IPage<RegionalPolicy> getByRegionAndType(String region, String policyType, Page<RegionalPolicy> page) {
        QueryWrapper<RegionalPolicy> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("region", region);
        
        if (StringUtils.isNotBlank(policyType)) {
            queryWrapper.eq("policy_type", policyType);
        }
        
        queryWrapper.eq("status", 1);
        queryWrapper.orderByDesc("publish_time");
        
        return policyMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    public IPage<RegionalPolicy> searchByKeyword(String keyword, Page<RegionalPolicy> page) {
        return policyMapper.searchByKeyword(page, keyword);
    }
    
    @Override
    public List<RegionalPolicy> recommendForUser(Long userId, int limit) {
        // 1. 获取用户最近浏览的政策类型和地区
        List<String> recentTypes = browseHistoryService.getUserRecentPolicyTypes(userId, 10);
        String userRegion = getUserRegion(userId);
        
        // 2. 根据用户浏览历史和地区推荐政策
        if (!recentTypes.isEmpty()) {
            return policyMapper.recommendByTypesAndRegion(recentTypes, userRegion, limit);
        }
        
        // 3. 如果没有浏览历史，返回热门政策
        return policyMapper.getHotPolicies(limit);
    }
    
    private String getUserRegion(Long userId) {
        // 获取用户所在地区的逻辑
        // ...
        return "默认地区";
    }
}
```

## 4. 用户收藏模块

### 4.1 收藏实体类

```java
@Data
@TableName("user_favorite")
public class UserFavorite implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("content_type")
    private String contentType;
    
    @TableField("content_id")
    private Long contentId;
    
    @TableField("create_time")
    private Date createTime;
}
```

### 4.2 收藏服务接口

```java
public interface UserFavoriteService extends IService<UserFavorite> {
    
    /**
     * 添加收藏
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 是否成功
     */
    boolean addFavorite(Long userId, String contentType, Long contentId);
    
    /**
     * 取消收藏
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 是否成功
     */
    boolean removeFavorite(Long userId, String contentType, Long contentId);
    
    /**
     * 检查是否已收藏
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 是否已收藏
     */
    boolean isFavorite(Long userId, String contentType, Long contentId);
    
    /**
     * 获取用户收藏列表
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<Map<String, Object>> getUserFavorites(Long userId, String contentType, Page<UserFavorite> page);
}
```

## 5. 前端核心组件

### 5.1 政策列表组件

```vue
<template>
  <div class="policy-list">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-select v-model="region" placeholder="选择地区" @change="handleFilter">
          <el-option v-for="item in regionOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        
        <el-select v-model="policyType" placeholder="政策类型" @change="handleFilter">
          <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        
        <el-input v-model="keyword" placeholder="搜索政策" class="search-input" @keyup.enter="handleSearch">
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
    </el-card>
    
    <div class="policy-items">
      <el-card v-for="policy in policies" :key="policy.id" class="policy-item" @click="viewDetail(policy.id)">
        <div class="policy-header">
          <h3 class="policy-title">{{ policy.title }}</h3>
          <el-tag size="small">{{ policy.policyLevel }}</el-tag>
        </div>
        
        <div class="policy-summary">{{ policy.summary }}</div>
        
        <div class="policy-footer">
          <span class="policy-region">{{ policy.region }}</span>
          <span class="policy-date">{{ formatDate(policy.publishTime) }}</span>
          <span class="policy-views">
            <i class="el-icon-view"></i> {{ policy.viewCount }}
          </span>
        </div>
      </el-card>
    </div>
    
    <div class="pagination-container">
      <el-pagination
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPolicyList, searchPolicies } from '@/api/policy';
import { formatDate } from '@/utils/date';

export default defineComponent({
  name: 'PolicyList',
  
  setup() {
    const policies = ref([]);
    const total = ref(0);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const region = ref('');
    const policyType = ref('');
    const keyword = ref('');
    
    const regionOptions = ref([
      { value: '', label: '全部地区' },
      { value: '北京', label: '北京' },
      { value: '上海', label: '上海' },
      // 更多地区选项...
    ]);
    
    const typeOptions = ref([
      { value: '', label: '全部类型' },
      { value: '资金扶持', label: '资金扶持' },
      { value: '税收优惠', label: '税收优惠' },
      { value: '创业培训', label: '创业培训' },
      // 更多类型选项...
    ]);
    
    const loadPolicies = async () => {
      try {
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          region: region.value,
          policyType: policyType.value
        };
        
        const res = await getPolicyList(params);
        policies.value = res.data.records;
        total.value = res.data.total;
      } catch (error) {
        ElMessage.error('获取政策列表失败');
        console.error(error);
      }
    };
    
    const handleSearch = async () => {
      if (!keyword.value.trim()) {
        loadPolicies();
        return;
      }
      
      try {
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          keyword: keyword.value
        };
        
        const res = await searchPolicies(params);
        policies.value = res.data.records;
        total.value = res.data.total;
      } catch (error) {
        ElMessage.error('搜索政策失败');
        console.error(error);
      }
    };
    
    const handleFilter = () => {
      currentPage.value = 1;
      loadPolicies();
    };
    
    const handlePageChange = (page: number) => {
      currentPage.value = page;
      if (keyword.value.trim()) {
        handleSearch();
      } else {
        loadPolicies();
      }
    };
    
    const viewDetail = (id: number) => {
      // 跳转到详情页
      window.location.href = `/policy/detail/${id}`;
    };
    
    onMounted(() => {
      loadPolicies();
    });
    
    return {
      policies,
      total,
      currentPage,
      pageSize,
      region,
      policyType,
      keyword,
      regionOptions,
      typeOptions,
      handleFilter,
      handleSearch,
      handlePageChange,
      viewDetail,
      formatDate
    };
  }
});
</script>
```

### 5.2 政策详情组件

```vue
<template>
  <div class="policy-detail">
    <el-card class="detail-card">
      <div class="detail-header">
        <h1 class="detail-title">{{ policy.title }}</h1>
        <div class="detail-meta">
          <el-tag size="small">{{ policy.policyLevel }}</el-tag>
          <span class="detail-region">{{ policy.region }}</span>
          <span class="detail-date">发布时间：{{ formatDate(policy.publishTime) }}</span>
          <span class="detail-source">来源：{{ policy.source }}</span>
          <span class="detail-views">
            <i class="el-icon-view"></i> {{ policy.viewCount }}
          </span>
        </div>
      </div>
      
      <div class="detail-summary">
        <h3>政策摘要</h3>
        <p>{{ policy.summary }}</p>
      </div>
      
      <div class="detail-content">
        <h3>政策内容</h3>
        <div v-html="policy.content"></div>
      </div>
      
      <div v-if="policy.attachment" class="detail-attachment">
        <h3>附件下载</h3>
        <el-button type="primary" size="small" @click="downloadAttachment">
          <i class="el-icon-download"></i> 下载附件
        </el-button>
      </div>
      
      <div v-if="policy.sourceUrl" class="detail-source-link">
        <h3>原文链接</h3>
        <a :href="policy.sourceUrl" target="_blank">{{ policy.sourceUrl }}</a>
      </div>
      
      <div class="detail-actions">
        <el-button type="primary" :icon="isFavorite ? 'el-icon-star-on' : 'el-icon-star-off'" @click="toggleFavorite">
          {{ isFavorite ? '已收藏' : '收藏' }}
        </el-button>
        <el-button type="info" icon="el-icon-share" @click="sharePolicy">分享</el-button>
      </div>
    </el-card>
    
    <el-card class="related-card">
      <div class="related-title">相关政策</div>
      <div class="related-list">
        <div v-for="item in relatedPolicies" :key="item.id" class="related-item" @click="goToPolicy(item.id)">
          <div class="related-item-title">{{ item.title }}</div>
          <div class="related-item-meta">
            <span>{{ item.region }}</span>
            <span>{{ formatDate(item.publishTime) }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { getPolicyDetail, getRelatedPolicies } from '@/api/policy';
import { addFavorite, removeFavorite, checkFavorite } from '@/api/favorite';
import { formatDate } from '@/utils/date';

export default defineComponent({
  name: 'PolicyDetail',
  
  setup() {
    const route = useRoute();
    const policyId = Number(route.params.id);
    
    const policy = ref({});
    const relatedPolicies = ref([]);
    const isFavorite = ref(false);
    
    const loadPolicyDetail = async () => {
      try {
        const res = await getPolicyDetail(policyId);
        policy.value = res.data;
      } catch (error) {
        ElMessage.error('获取政策详情失败');
        console.error(error);
      }
    };
    
    const loadRelatedPolicies = async () => {
      try {
        const res = await getRelatedPolicies(policyId);
        relatedPolicies.value = res.data;
      } catch (error) {
        console.error('获取相关政策失败', error);
      }
    };
    
    const checkIsFavorite = async () => {
      try {
        const res = await checkFavorite({
          contentType: 'policy',
          contentId: policyId
        });
        isFavorite.value = res.data;
      } catch (error) {
        console.error('检查收藏状态失败', error);
      }
    };
    
    const toggleFavorite = async () => {
      try {
        if (isFavorite.value) {
          await removeFavorite({
            contentType: 'policy',
            contentId: policyId
          });
          isFavorite.value = false;
          ElMessage.success('取消收藏成功');
        } else {
          await addFavorite({
            contentType: 'policy',
            contentId: policyId
          });
          isFavorite.value = true;
          ElMessage.success('收藏成功');
        }
      } catch (error) {
        ElMessage.error(isFavorite.value ? '取消收藏失败' : '收藏失败');
        console.error(error);
      }
    };
    
    const downloadAttachment = () => {
      window.open(policy.value.attachment, '_blank');
    };
    
    const sharePolicy = () => {
      // 实现分享功能
      const url = window.location.href;
      // 可以集成第三方分享SDK或使用原生Web Share API
      if (navigator.share) {
        navigator.share({
          title: policy.value.title,
          text: policy.value.summary,
          url: url
        });
      } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(url).then(() => {
          ElMessage.success('链接已复制到剪贴板');
        });
      }
    };
    
    const goToPolicy = (id: number) => {
      window.location.href = `/policy/detail/${id}`;
    };
    
    onMounted(() => {
      loadPolicyDetail();
      loadRelatedPolicies();
      checkIsFavorite();
    });
    
    return {
      policy,
      relatedPolicies,
      isFavorite,
      toggleFavorite,
      downloadAttachment,
      sharePolicy,
      goToPolicy,
      formatDate
    };
  }
});
</script>
```

## 6. 缓存策略实现

### 6.1 Spring Cache配置

```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .expireAfterWrite(1, TimeUnit.HOURS)
                .initialCapacity(100)
                .maximumSize(1000));
        return cacheManager;
    }
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);
        
        // 使用Jackson2JsonRedisSerializer序列化值
        Jackson2JsonRedisSerializer<Object> serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance, ObjectMapper.DefaultTyping.NON_FINAL);
        serializer.setObjectMapper(mapper);
        
        template.setValueSerializer(serializer);
        template.setKeySerializer(new StringRedisSerializer());
        template.afterPropertiesSet();
        
        return template;
    }
}
```

### 6.2 前端缓存实现

```typescript
// src/utils/cache.ts

interface CacheItem<T> {
  data: T;
  expiry: number;
}

export class LocalCache {
  /**
   * 设置缓存
   * @param key 缓存键
   * @param data 缓存数据
   * @param ttl 过期时间（秒）
   */
  static set<T>(key: string, data: T, ttl: number = 3600): void {
    const now = new Date().getTime();
    const item: CacheItem<T> = {
      data,
      expiry: now + ttl * 1000
    };
    localStorage.setItem(key, JSON.stringify(item));
  }
  
  /**
   * 获取缓存
   * @param key 缓存键
   * @returns 缓存数据，如果过期或不存在则返回null
   */
  static get<T>(key: string): T | null {
    const itemStr = localStorage.getItem(key);
    if (!itemStr) {
      return null;
    }
    
    try {
      const item: CacheItem<T> = JSON.parse(itemStr);
      const now = new Date().getTime();
      
      // 检查是否过期
      if (now > item.expiry) {
        localStorage.removeItem(key);
        return null;
      }
      
      return item.data;
    } catch (e) {
      localStorage.removeItem(key);
      return null;
    }
  }
  
  /**
   * 删除缓存
   * @param key 缓存键
   */
  static remove(key: string): void {
    localStorage.removeItem(key);
  }
  
  /**
   * 清除所有缓存
   */
  static clear(): void {
    localStorage.clear();
  }
}
```

## 7. 全局异常处理

```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<?> handleBusinessException(BusinessException e) {
        logger.error("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(MethodArgumentNotValidException
    系统架构相关
前后端一体化管理架构
将前端源码（Vue.js项目）直接集成在Spring Boot项目的 src/main/resources/static/目录下
简化部署流程，解决跨域问题，便于小型团队协作开发和维护
Spring Boot
项目使用的Java后端框架，版本2.7.x
提供自动配置、内嵌服务器等特性，简化Java应用开发
MyBatis Plus
基于MyBatis的增强工具，版本3.5.x
提供CRUD操作的自动实现、分页查询等功能，简化数据库操作
JWT (JSON Web Token)
用于身份认证的令牌机制
实现无状态认证，令牌中包含用户信息和权限信息
Nacos
阿里巴巴开发的服务发现和配置管理平台
在项目中用于服务注册与发现，支持分布式系统架构
Spring Cloud Alibaba
阿里巴巴提供的微服务解决方案
包含Nacos、Sentinel等组件，与Spring Cloud生态兼容
Vue.js + TypeScript
前端开发框架和语言
用于构建用户界面，提供响应式数据绑定和组件化开发
业务模块相关
管理员用户 (AdminUser)
后台管理系统的用户
具有管理政策、用户、文章等权限
应用用户 (AppUser)
前端系统的普通用户
可以浏览政策、参与讨论、收藏政策等
地区创业政策 (RegionalPolicy)
系统核心内容，按地区分类的创业政策信息
包含标题、内容、摘要、地区、政策级别、类型等属性
文章信息 (ArticleInfo)
系统中的文章内容
可能包含创业指南、经验分享等
文章类型 (ArticleType)
文章的分类信息
用于组织和筛选文章
文章评论 (ArticleComment)
用户对文章的评论
支持用户交流和讨论
创业论坛 (EntrepreneurshipForum)
用户交流创业经验的平台
分为用户项目(type=0)和成功案例(type=1)两种类型
留言板 (MessageBoard)
用户向管理员提问咨询的功能
管理员可以回复用户留言
资讯信息 (NewsInfo)
创业相关的新闻资讯
由管理员发布和管理
用户收藏 (UserFavorite)
用户收藏的政策或文章
记录用户ID、内容类型、内容ID等信息
浏览历史 (BrowseHistory)
记录用户的浏览行为
用于个性化推荐和用户行为分析
用户令牌 (UserToken)
存储用户的JWT令牌信息
用于身份验证和会话管理
投票信息 (VotingInfo)
系统中的投票功能数据
可能用于收集用户意见或进行调查
系统配置 (Config)
系统的各种配置信息
由管理员在后台设置和管理
技术实现相关
UserDetails
Spring Security中表示用户详情的接口
包含用户名、密码、权限等信息
GrantedAuthority
Spring Security中表示授权的接口
在项目中用于定义用户角色和权限
QueryWrapper
MyBatis Plus提供的查询条件包装器
用于构建SQL查询条件
ApiResponse
统一的API响应格式
包含状态码、消息和数据
LambdaQueryWrapper
MyBatis Plus提供的Lambda风格查询条件包装器
提供类型安全的查询方式
Page
MyBatis Plus提供的分页对象
用于实现分页查询
PasswordEncoder
Spring Security中的密码编码器接口
用于密码的加密和验证
NoOpPasswordEncoder
不进行加密的密码编码器
仅用于开发环境，直接比较明文密码
EnableDiscoveryClient
Spring Cloud注解，启用服务发现客户端
使应用能够注册到Nacos等服务注册中心
EnableFeignClients
Spring Cloud注解，启用Feign客户端
用于简化服务间调用
BOM (Bill of Materials)
Maven依赖管理机制
用于统一管理依赖版本，避免版本冲突
这些名词涵盖了项目的架构设计、业务模块和技术实现等方面，反映了项目是一个基于Spring Boot和Vue.js的前后端一体化创业政策信息服务平台，采用了现代化的技术栈和架构设计。
