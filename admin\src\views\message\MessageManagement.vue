<template>
  <div class="message-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="留言内容">
            <el-input v-model="searchForm.content" placeholder="请输入留言内容关键词" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择留言状态" clearable style="width: 220px">
              <el-option label="未回复" value="0" />
              <el-option label="已回复" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-container">
          <el-button type="danger" :disabled="selectedMessages.length === 0" @click="handleBatchDelete">
            批量删除
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="messageList"
        border
        style="width: 100%"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="content" label="留言内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="createTime" label="留言时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.replyContent ? 'success' : 'warning'">
              {{ scope.row.replyContent ? '已回复' : '未回复' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button 
              link 
              type="primary" 
              size="small" 
              @click="handleReply(scope.row)"
              v-if="!scope.row.reply"
            >
              回复
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="留言详情"
      width="600px"
    >
      <div v-if="currentMessage" class="message-detail">
        <div class="message-meta">
          <p><strong>用户：</strong>{{ currentMessage.username }}</p>
          <p><strong>时间：</strong>{{ formatDateTime(currentMessage.createTime) }}</p>
        </div>
        <div class="message-content">
          <h3>留言内容：</h3>
          <p>{{ currentMessage.content }}</p>
        </div>
        <div class="message-reply" v-if="currentMessage.replyContent">
          <h3>回复内容：</h3>
          <p>{{ currentMessage.replyContent }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialogVisible"
      title="回复留言"
      width="600px"
      @close="closeReplyDialog"
    >
      <div v-if="currentMessage" class="reply-form">
        <div class="message-content">
          <h3>留言内容：</h3>
          <p>{{ currentMessage.content }}</p>
        </div>
        <el-form
          ref="replyFormRef"
          :model="replyForm"
          :rules="replyRules"
          label-width="80px"
        >
          <el-form-item label="回复内容" prop="replyContent">
            <el-input
              v-model="replyForm.replyContent"
              type="textarea"
              :rows="5"
              placeholder="请输入回复内容"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeReplyDialog">取消</el-button>
          <el-button type="primary" @click="submitReply">提交回复</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listMessages, getMessage, replyMessage, deleteMessage, batchDeleteMessages } from '@/api/message'

// 加载状态
const tableLoading = ref(false)

// 表单引用，用于表单校验
const replyFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  username: '',
  content: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 留言列表数据
const messageList = ref([])

// 选中的留言
const selectedMessages = ref([])

// 对话框控制
const detailDialogVisible = ref(false)
const replyDialogVisible = ref(false)

// 当前留言（用于详情查看和回复）
const currentMessage = ref(null)

// 回复表单
const replyForm = reactive({
  replyContent: ''
})

// 回复表单校验规则
const replyRules = {
  replyContent: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 初始化加载数据
onMounted(() => {
  fetchMessageList()
})

// 获取留言列表
const fetchMessageList = async () => {
  tableLoading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchForm
    }

    const res = await listMessages(params)

    // 打印拦截器处理后的响应对象
    console.log('Processed API Response (data part):', res)

    // 直接检查 res 是否有效，以及 list 和 total 字段
    if (res && Array.isArray(res.list) && typeof res.total !== 'undefined') {
      messageList.value = res.list
      pagination.total = res.total
      console.log('Data loaded successfully from processed response.')
    } else {
      // 如果 res 无效或缺少必要字段
      messageList.value = []
      pagination.total = 0
      ElMessage.error('加载留言列表失败: 无效的数据结构')
      console.error('Invalid data structure received after interceptor:', res)
    }
  } catch (error) {
    // 捕获API调用或处理过程中的异常
    // 注意：这里的error可能是拦截器reject出来的，也可能是listMessages内部错误
    console.error('获取或处理留言列表时发生异常:', error)
    messageList.value = []
    pagination.total = 0
    // 尝试显示拦截器reject的错误消息，否则显示通用错误
    ElMessage.error(error.message || '获取留言列表时发生异常')
  } finally {
    tableLoading.value = false
  }
}

// 查询
const handleSearch = () => {
  pagination.current = 1 // 重置到第一页
  fetchMessageList()
}

// 重置搜索
const resetSearch = () => {
  // 重置搜索表单
  searchForm.username = ''
  searchForm.content = ''
  searchForm.status = ''
  
  // 重新查询
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchMessageList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  fetchMessageList()
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedMessages.value = selection
}

// 查看留言详情
const handleView = async (row) => {
  try {
    const res = await getMessage(row.id)
    currentMessage.value = res
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取留言详情失败:', error)
    ElMessage.error('获取留言详情失败')
  }
}

// 回复留言
const handleReply = async (row) => {
  try {
    const res = await getMessage(row.id)
    currentMessage.value = res
    replyForm.replyContent = ''
    replyDialogVisible.value = true
  } catch (error) {
    console.error('获取留言详情失败:', error)
    ElMessage.error('获取留言详情失败')
  }
}

// 关闭回复对话框
const closeReplyDialog = () => {
  replyDialogVisible.value = false
  if (replyFormRef.value) {
      replyFormRef.value.resetFields();
  }
  replyForm.replyContent = ''
}

// 提交回复
const submitReply = async () => {
  if (!replyFormRef.value) return
  
  await replyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await replyMessage(currentMessage.value.id, replyForm)
        ElMessage.success('回复成功')
        
        // 关闭对话框并刷新列表
        replyDialogVisible.value = false
        fetchMessageList()
      } catch (error) {
        console.error('回复留言失败:', error)
        ElMessage.error('回复留言失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 删除留言
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除该留言吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteMessage(row.id)
      ElMessage.success('删除成功')
      
      // 如果删除的是当前页的最后一条数据且不是第一页，则跳转到上一页
      if (messageList.value.length === 1 && pagination.current > 1) {
        pagination.current--
      }
      fetchMessageList()
    } catch (error) {
      console.error('删除留言失败:', error)
      ElMessage.error('删除留言失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 批量删除留言
const handleBatchDelete = () => {
  if (selectedMessages.value.length === 0) {
    ElMessage.warning('请至少选择一条留言')
    return
  }
  
  ElMessageBox.confirm(
    `确定要删除选中的 ${selectedMessages.value.length} 条留言吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const ids = selectedMessages.value.map(item => item.id)
      await batchDeleteMessages(ids)
      ElMessage.success('批量删除成功')
      fetchMessageList()
    } catch (error) {
      console.error('批量删除留言失败:', error)
      ElMessage.error('批量删除留言失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  
  // 如果是字符串类型，先转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  
  // 如果转换后不是有效日期
  if (isNaN(date.getTime())) return dateTime
  
  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}
</script>

<style scoped>
.message-management-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.search-form {
  flex: 1;
}

.operation-container {
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 留言详情样式 */
.message-detail {
  padding: 10px;
}

.message-meta {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 15px;
}

.message-content,
.message-reply {
  margin-bottom: 20px;
}

.message-content h3,
.message-reply h3 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}

.reply-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
  margin-top: 5px;
}

/* 回复表单样式 */
.reply-form {
  padding: 10px;
}
</style> 