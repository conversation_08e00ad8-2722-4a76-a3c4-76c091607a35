/**
 * 创业论坛相关API
 */
import request from '../utils/request'

/**
 * 获取论坛列表
 * @param {Object} params 查询参数
 * @param {Number} params.type 类型：0-用户项目，1-成功案例
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页条数
 * @returns {Promise<Object>} 论坛列表数据
 */
export function fetchForumList({ type, page = 1, limit = 10 } = {}) {
  const params = {};
  if (type !== undefined) params.type = type;
  params.page = page;
  params.limit = limit;
  
  return request({
    url: '/api/forum',
    method: 'get',
    params
  });
}

/**
 * 获取论坛详情
 * @param {Number} id 论坛ID
 * @returns {Promise<Object>} 论坛详情
 */
export function fetchForumDetail(id) {
  return request({
    url: `/api/forum/${id}`,
    method: 'get'
  });
}

/**
 * 发布用户项目
 * @param {Object} data 项目数据
 * @returns {Promise<Object>} 发布结果
 */
export function publishUserProject(data) {
  return request({
    url: '/api/forum',
    method: 'post',
    data
  });
} 