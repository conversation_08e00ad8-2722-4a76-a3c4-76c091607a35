import request from '@/utils/request' // 假设 request 工具路径

// 分页查询留言列表
export function listMessages(params) {
  return request({
    url: '/admin/messages',
    method: 'get',
    params // 使用 params 传递查询参数
  })
}

// 根据ID获取留言详情
export function getMessage(id) {
  return request({
    url: `/admin/messages/${id}`,
    method: 'get'
  })
}

// 回复留言
export function replyMessage(id, data) {
  return request({
    url: `/admin/messages/${id}/reply`,
    method: 'post',
    data
  })
}

// 删除留言
export function deleteMessage(id) {
  return request({
    url: `/admin/messages/${id}`,
    method: 'delete'
  })
}

// 批量删除留言
export function batchDeleteMessages(ids) {
  return request({
    url: '/admin/messages/batch',
    method: 'delete',
    data: { ids }
  })
} 