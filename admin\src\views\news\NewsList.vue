<template>
  <div class="news-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="标题">
            <el-input v-model="searchForm.title" placeholder="请输入标题" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-container">
          <el-button type="primary" @click="openEditDialog()">新增资讯</el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="newsList"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="introduction" label="简介" min-width="200" show-overflow-tooltip />
        <el-table-column prop="picture" label="图片" width="100">
          <template #default="scope">
            <img v-if="scope.row.picture" :src="scope.row.picture" alt="图片" style="width:60px;height:60px;object-fit:cover;border-radius:4px;" />
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="openEditDialog(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="editDialogVisible" :title="editForm.id ? '编辑资讯' : '新增资讯'" width="600px" @close="resetEditForm">
      <el-form :model="editForm" label-width="80px" :rules="rules" ref="editFormRef">
        <el-form-item label="标题" prop="title">
          <el-input v-model="editForm.title" />
        </el-form-item>
        <el-form-item label="简介" prop="introduction">
          <el-input v-model="editForm.introduction" />
        </el-form-item>
        <el-form-item label="图片" prop="picture">
          <el-upload
            class="news-cover-uploader"
            action="/api/uploads/file"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :before-upload="beforeCoverUpload"
          >
            <img v-if="editForm.picture" :src="editForm.picture" class="news-cover" />
            <el-icon v-else class="news-cover-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input type="textarea" v-model="editForm.content" :rows="6" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitEdit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

const newsList = ref<any[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)
const tableLoading = ref(false)
const searchForm = reactive({ title: '' })

const editDialogVisible = ref(false)
const editForm = reactive<any>({})
const editFormRef = ref()

const rules = {
  title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
}

const getAuthHeader = () => {
  const token = localStorage.getItem('token')
  return token ? `Bearer ${token}` : ''
}

const fetchList = async () => {
  tableLoading.value = true
  let url = `/api/admin/news?page=${page.value}&limit=${pageSize.value}`
  if (searchForm.title) url += `&title=${encodeURIComponent(searchForm.title)}`
  const res = await fetch(url, {
    headers: {
      'Authorization': getAuthHeader()
    }
  })
  const text = await res.text()
  let data
  try {
    data = JSON.parse(text)
  } catch (e) {
    ElMessage.error('接口返回非JSON，可能是404、未登录或服务异常')
    tableLoading.value = false
    return
  }
  if (data.code === 200) {
    newsList.value = data.data.list || data.data.records || []
    total.value = data.data.total
  }
  tableLoading.value = false
}

const handleSearch = () => {
  page.value = 1
  fetchList()
}
const resetSearch = () => {
  searchForm.title = ''
  handleSearch()
}
const handleCurrentChange = (val: number) => {
  page.value = val
  fetchList()
}
const handleSizeChange = (val: number) => {
  pageSize.value = val
  page.value = 1
  fetchList()
}

const openEditDialog = (row?: any) => {
  if (row) {
    Object.assign(editForm, row)
  } else {
    Object.assign(editForm, { id: undefined, title: '', introduction: '', picture: '', content: '' })
  }
  editDialogVisible.value = true
}
const resetEditForm = () => {
  Object.assign(editForm, { id: undefined, title: '', introduction: '', picture: '', content: '' })
  editFormRef.value?.clearValidate?.()
}
const submitEdit = async () => {
  const valid = await editFormRef.value?.validate?.()
  if (!valid) return
  let method = editForm.id ? 'PUT' : 'POST'
  let url = '/api/admin/news' + (editForm.id ? `/${editForm.id}` : '')
  const res = await fetch(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': getAuthHeader()
    },
    body: JSON.stringify(editForm)
  })
  const text = await res.text()
  let data
  try {
    data = JSON.parse(text)
  } catch (e) {
    ElMessage.error('接口返回非JSON，可能是404、未登录或服务异常')
    return
  }
  if (data.code === 200) {
    ElMessage.success('保存成功')
    editDialogVisible.value = false
    fetchList()
  } else {
    ElMessage.error(data.msg || '保存失败')
  }
}
const handleDelete = (id: number) => {
  ElMessageBox.confirm('确定要删除这条资讯吗？', '提示', { type: 'warning' })
    .then(async () => {
      const res = await fetch(`/api/admin/news/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': getAuthHeader()
        }
      })
      const text = await res.text()
      let data
      try {
        data = JSON.parse(text)
      } catch (e) {
        ElMessage.error('接口返回非JSON，可能是404、未登录或服务异常')
        return
      }
      if (data.code === 200) {
        ElMessage.success('删除成功')
        fetchList()
      } else {
        ElMessage.error(data.msg || '删除失败')
      }
    })
}
const handleCoverSuccess = (res, file) => {
  editForm.picture = res.url || (res.data && res.data.url) || ''
}
const beforeCoverUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isImage) {
    ElMessage.error('图片只能是图片格式!')
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
  }
  return isImage && isLt2M
}
onMounted(fetchList)
</script>

<style scoped>
.news-management-container {
  padding: 20px;
}
.search-card,
.table-card {
  margin-bottom: 20px;
}
.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.search-form {
  flex: 1;
}
.operation-container {
  margin-left: 20px;
}
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
.news-cover-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}
.news-cover-uploader:hover {
  border-color: #409eff;
}
.news-cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}
.news-cover {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}
</style> 