import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 8888,
    open: true,
    proxy: {
      // 代理API请求到后端
      '/api': {
        target: 'http://localhost:8080', // 修改为你的后端地址
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '/api')
      },
      // 添加上传接口代理
      '/uploads': {
        target: 'http://localhost:8080', // 后端地址
        changeOrigin: true,
        rewrite: path => path
      }
    }
  }
}) 