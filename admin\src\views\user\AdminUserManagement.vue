<template>
  <div class="admin-user-container">
    <div class="search-box">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <div class="tool-bar">
        <el-button type="primary" @click="handleAdd">新增管理员</el-button>
      </div>

      <el-table v-loading="loading" :data="adminList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="realName" label="真实姓名" width="120"></el-table-column>
        <el-table-column prop="email" label="邮箱"></el-table-column>
        <el-table-column prop="phone" label="手机号"></el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleResetPassword(scope.row)">重置密码</el-button>
            <el-button 
              :type="scope.row.status === 1 ? 'danger' : 'success'" 
              size="small" 
              @click="handleStatusChange(scope.row)"
            >
              {{ scope.row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 添加/编辑管理员对话框 -->
    <el-dialog 
      :title="dialogTitle" 
      v-model="dialogVisible" 
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="adminForm" :rules="rules" ref="adminFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="adminForm.username" placeholder="请输入用户名" :disabled="operationType === 'edit'"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="operationType === 'add'">
          <el-input v-model="adminForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="adminForm.realName" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="adminForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="adminForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="adminForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  listAdminUsers,
  getAdminUser,
  addAdminUser,
  updateAdminUser,
  deleteAdminUser,
  updateAdminStatus,
  resetAdminPassword
} from '@/api/user'

export default {
  name: 'AdminUserManagement',
  setup() {
    const adminFormRef = ref(null)
    
    const state = reactive({
      // 加载状态
      loading: false,
      // 管理员列表
      adminList: [],
      // 总数
      total: 0,
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        username: '',
        status: ''
      },
      // 操作类型：add-新增，edit-编辑
      operationType: 'add',
      // 对话框标题
      dialogTitle: '新增管理员',
      // 对话框可见性
      dialogVisible: false,
      // 表单对象
      adminForm: {
        id: undefined,
        username: '',
        password: '',
        realName: '',
        email: '',
        phone: '',
        status: 1
      },
      // 表单校验规则
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      }
    })

    // 获取管理员列表
    const getList = async () => {
      state.loading = true
      try {
        const res = await listAdminUsers(state.queryParams)
        state.adminList = res.data.list
        state.total = res.data.total
      } catch (error) {
        console.error('获取管理员列表失败', error)
      } finally {
        state.loading = false
      }
    }

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1
      getList()
    }

    // 重置查询条件
    const resetQuery = () => {
      state.queryParams.username = ''
      state.queryParams.status = ''
      handleQuery()
    }

    // 每页数量改变事件
    const handleSizeChange = (val) => {
      state.queryParams.limit = val
      getList()
    }

    // 当前页改变事件
    const handleCurrentChange = (val) => {
      state.queryParams.page = val
      getList()
    }

    // 新增管理员按钮点击事件
    const handleAdd = () => {
      state.operationType = 'add'
      state.dialogTitle = '新增管理员'
      state.adminForm = {
        id: undefined,
        username: '',
        password: '',
        realName: '',
        email: '',
        phone: '',
        status: 1
      }
      state.dialogVisible = true
    }

    // 编辑管理员按钮点击事件
    const handleEdit = async (row) => {
      state.operationType = 'edit'
      state.dialogTitle = '编辑管理员'
      state.dialogVisible = true
      
      try {
        const res = await getAdminUser(row.id)
        state.adminForm = { ...res.data }
      } catch (error) {
        console.error('获取管理员详情失败', error)
      }
    }

    // 重置密码按钮点击事件
    const handleResetPassword = (row) => {
      ElMessageBox.confirm(
        `确定要重置 ${row.username} 的密码吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await resetAdminPassword(row.id)
          ElMessage.success('密码重置成功')
        } catch (error) {
          console.error('密码重置失败', error)
        }
      }).catch(() => {})
    }

    // 状态改变按钮点击事件
    const handleStatusChange = (row) => {
      const statusText = row.status === 1 ? '禁用' : '启用'
      ElMessageBox.confirm(
        `确定要${statusText}管理员"${row.username}"吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await updateAdminStatus(row.id, row.status === 1 ? 0 : 1)
          ElMessage.success(`${statusText}成功`)
          getList()
        } catch (error) {
          console.error(`${statusText}失败`, error)
        }
      }).catch(() => {})
    }

    // 删除按钮点击事件
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除管理员"${row.username}"吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteAdminUser(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          console.error('删除失败', error)
        }
      }).catch(() => {})
    }

    // 提交表单
    const submitForm = () => {
      adminFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            if (state.operationType === 'add') {
              await addAdminUser(state.adminForm)
              ElMessage.success('新增成功')
            } else {
              await updateAdminUser(state.adminForm.id, state.adminForm)
              ElMessage.success('更新成功')
            }
            state.dialogVisible = false
            getList()
          } catch (error) {
            console.error('提交表单失败', error)
          }
        }
      })
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state),
      adminFormRef,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleResetPassword,
      handleStatusChange,
      handleDelete,
      submitForm
    }
  }
}
</script>

<style scoped>
.admin-user-container {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-box {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tool-bar {
  margin-bottom: 15px;
}

.pagination-box {
  margin-top: 15px;
  text-align: right;
}
</style> 