import request from '@/utils/request'

// 分页查询论坛帖子列表
export function listForumPosts(params) {
  return request({
    url: '/admin/forum',
    method: 'get',
    params
  })
}

// 获取帖子详情
export function getForumPost(id) {
  return request({
    url: `/admin/forum/posts/${id}`,
    method: 'get'
  })
}

// 更新帖子
export function updateForumPost(id, data) {
  return request({
    url: `/admin/forum/${id}`,
    method: 'put',
    data
  })
}

// 删除帖子
export function deleteForumPost(id) {
  return request({
    url: `/admin/forum/${id}`,
    method: 'delete'
  })
}

// 设置帖子置顶状态
export function setPostTop(id, isTop) {
  return request({
    url: `/admin/forum/posts/${id}/top`,
    method: 'put',
    data: { isTop }
  })
}

// 设置帖子精华状态
export function setPostEssence(id, isEssence) {
  return request({
    url: `/admin/forum/posts/${id}/essence`,
    method: 'put',
    data: { isEssence }
  })
}

// 获取帖子评论列表
export function listPostComments(postId, params) {
  return request({
    url: `/admin/forum/posts/${postId}/comments`,
    method: 'get',
    params
  })
}

// 删除帖子评论
export function deletePostComment(postId, commentId) {
  return request({
    url: `/admin/forum/posts/${postId}/comments/${commentId}`,
    method: 'delete'
  })
}

// 获取论坛分类列表
export function listForumCategories() {
  return request({
    url: '/admin/forum/categories',
    method: 'get'
  })
}

// 新增论坛分类
export function addForumCategory(data) {
  return request({
    url: '/admin/forum/categories',
    method: 'post',
    data
  })
}

// 更新论坛分类
export function updateForumCategory(id, data) {
  return request({
    url: `/admin/forum/categories/${id}`,
    method: 'put',
    data
  })
}

// 删除论坛分类
export function deleteForumCategory(id) {
  return request({
    url: `/admin/forum/categories/${id}`,
    method: 'delete'
  })
}

// 新增论坛帖子
export function addForumPost(data) {
  return request({
    url: '/admin/forum',
    method: 'post',
    data
  })
} 