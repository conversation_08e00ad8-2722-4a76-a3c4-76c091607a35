<template>
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h2 class="auth-title">欢迎使用创业政策平台</h2>
        <p class="auth-subtitle">请登录或注册以获取更多功能</p>
      </div>
      
      <div class="tabs">
        <div 
          class="tab" 
          :class="{ active: activeTab === 'login' }" 
          @click="activeTab = 'login'"
        >
          登录
        </div>
        <div 
          class="tab" 
          :class="{ active: activeTab === 'register' }" 
          @click="activeTab = 'register'"
        >
          注册
        </div>
      </div>
      
      <!-- 登录表单 -->
      <div v-if="activeTab === 'login'" class="form-container">
        <div class="form-group">
          <label>用户名</label>
          <div class="input-wrapper">
            <i class="input-icon user-icon">👤</i>
            <input 
              type="text" 
              v-model="loginForm.username" 
              placeholder="请输入用户名"
              @keyup.enter="handleLogin"
            />
          </div>
        </div>
        <div class="form-group">
          <label>密码</label>
          <div class="input-wrapper">
            <i class="input-icon lock-icon">🔒</i>
            <input 
              type="password" 
              v-model="loginForm.password" 
              placeholder="请输入密码"
              @keyup.enter="handleLogin"
            />
          </div>
        </div>
        
        <div class="form-options">
          <label class="checkbox-label">
            <input type="checkbox" v-model="rememberMe" />
            <span>记住我</span>
          </label>
          <a href="#" class="forgot-link">忘记密码?</a>
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" @click="handleLogin" :disabled="loading">
            <span class="btn-text">{{ loading ? '登录中...' : '登录' }}</span>
            <span v-if="!loading" class="btn-icon">→</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </div>
        
        <div v-if="loginError" class="error-message">
          <i class="error-icon">⚠️</i>
          <span>{{ loginError }}</span>
        </div>
      </div>
      
      <!-- 注册表单 -->
      <div v-if="activeTab === 'register'" class="form-container register-form">
        <div class="form-group">
          <label>用户名 <span class="required">*</span></label>
          <div class="input-wrapper">
            <i class="input-icon user-icon">👤</i>
            <input 
              type="text" 
              v-model="registerForm.username" 
              placeholder="请输入用户名（必填）"
            />
          </div>
        </div>
        <div class="form-group">
          <label>密码 <span class="required">*</span></label>
          <div class="input-wrapper">
            <i class="input-icon lock-icon">🔒</i>
            <input 
              type="password" 
              v-model="registerForm.password" 
              placeholder="请输入密码（必填）"
            />
          </div>
        </div>
        <div class="form-group">
          <label>确认密码 <span class="required">*</span></label>
          <div class="input-wrapper">
            <i class="input-icon lock-icon">🔒</i>
            <input 
              type="password" 
              v-model="passwordConfirm" 
              placeholder="请再次输入密码（必填）"
            />
          </div>
        </div>
        
        <div class="form-row">
          <div class="form-group half">
            <label>姓名 <span class="required">*</span></label>
            <div class="input-wrapper">
              <i class="input-icon">📝</i>
              <input 
                type="text" 
                v-model="registerForm.fullName" 
                placeholder="请输入真实姓名"
              />
            </div>
          </div>
          <div class="form-group half">
            <label>性别</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" v-model="registerForm.gender" value="男" />
                <span>男</span>
              </label>
              <label class="radio-label">
                <input type="radio" v-model="registerForm.gender" value="女" />
                <span>女</span>
              </label>
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label>邮箱 <span class="required">*</span></label>
          <div class="input-wrapper">
            <i class="input-icon">✉️</i>
            <input 
              type="email" 
              v-model="registerForm.email" 
              placeholder="请输入邮箱地址"
            />
          </div>
        </div>
        <div class="form-group">
          <label>手机号码</label>
          <div class="input-wrapper">
            <i class="input-icon">📱</i>
            <input 
              type="tel" 
              v-model="registerForm.phone" 
              placeholder="请输入手机号码"
            />
          </div>
        </div>
        
        <div class="form-actions">
          <button class="btn btn-primary" @click="handleRegister" :disabled="loading">
            <span class="btn-text">{{ loading ? '注册中...' : '完成注册' }}</span>
            <span v-if="!loading" class="btn-icon">→</span>
            <span v-else class="loading-spinner"></span>
          </button>
        </div>
        
        <div v-if="registerError" class="error-message">
          <i class="error-icon">⚠️</i>
          <span>{{ registerError }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { login, register } from '../api/user'
import { getToken, setToken, saveLoginInfo, checkLoginStatus } from '../utils/auth'

const router = useRouter()
const activeTab = ref('login')
const loading = ref(false)
const loginError = ref('')
const registerError = ref('')
const passwordConfirm = ref('')
const rememberMe = ref(false)

// 登录表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 注册表单数据
const registerForm = reactive({
  username: '',
  password: '',
  fullName: '',
  gender: '男',
  email: '',
  phone: '',
  avatar: '' // 头像默认为空，可以后续在个人中心设置
})

// 初始化记住我功能和检查登录状态
onMounted(async () => {
  // 检查是否有保存的记住我设置
  const savedRememberMe = localStorage.getItem('rememberMe')
  if (savedRememberMe === 'true') {
    rememberMe.value = true
    const savedUsername = localStorage.getItem('username')
    if (savedUsername) {
      loginForm.username = savedUsername
    }
  }
  
  // 检查登录状态，如果已登录且当前在登录页，则跳转到首页
  try {
    const token = localStorage.getItem('token')
    if (token) {
      console.log('已有token，校验登录状态...')
      const status = await checkLoginStatus()
      if (status && status.loggedIn) {
        console.log('登录状态有效，跳转到首页')
        router.push('/')
      } else {
        console.log('token无效，清除登录信息')
        localStorage.removeItem('token')
      }
    } else {
      console.log('无token，停留在登录页面')
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
  }
})

// 登录处理
const handleLogin = async () => {
  // 表单验证
  if (!loginForm.username) {
    loginError.value = '请输入用户名'
    return
  }
  if (!loginForm.password) {
    loginError.value = '请输入密码'
    return
  }
  
  loading.value = true
  loginError.value = ''
  
  try {
    const res = await login(loginForm)
    console.log('登录响应:', res)
    
    // 从响应中提取token
    let token = null
    
    // 处理不同的响应格式
    if (res && res.token) {
      // 直接返回 {token: "xxx"} 格式
      token = res.token
    } else if (res && res.data && res.data.token) {
      // 返回 {code: 200, data: {token: "xxx"}} 格式
      token = res.data.token
    } else if (res && res.code === 200 && typeof res.data === 'string') {
      // 返回 {code: 200, data: "token字符串"} 格式
      token = res.data
    }
    
    if (token) {
      // 存储token
      setToken(token)
      
      // 存储"记住我"设置
      saveLoginInfo(loginForm.username, rememberMe.value)
      
      // 显示成功消息
      alert('登录成功')
      
      // 处理重定向
      const redirectPath = router.currentRoute.value.query.redirect
      if (redirectPath) {
        console.log('登录成功，重定向到:', redirectPath)
        router.push(redirectPath.toString()) // 转为字符串确保类型安全
      } else {
        // 没有重定向参数时默认跳转到首页
        console.log('登录成功，跳转到首页')
        router.push('/')
      }
    } else {
      loginError.value = '登录失败，未获取到有效token'
    }
  } catch (error) {
    console.error('登录异常:', error)
    loginError.value = error.message || '登录发生异常，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  // 表单验证
  if (!registerForm.username) {
    registerError.value = '请输入用户名'
    return
  }
  if (!registerForm.password) {
    registerError.value = '请输入密码'
    return
  }
  if (registerForm.password !== passwordConfirm.value) {
    registerError.value = '两次输入的密码不一致'
    return
  }
  if (!registerForm.fullName) {
    registerError.value = '请输入姓名'
    return
  }
  if (!registerForm.email) {
    registerError.value = '请输入邮箱'
    return
  }
  if (!/^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/.test(registerForm.email)) {
    registerError.value = '请输入有效的邮箱地址'
    return
  }
  if (registerForm.phone && !/^1[3-9]\d{9}$/.test(registerForm.phone)) {
    registerError.value = '请输入有效的手机号码'
    return
  }
  
  loading.value = true
  registerError.value = ''
  
  try {
    const res = await register(registerForm)
    if (res.code === 200) {
      // 注册成功后切换到登录页
      activeTab.value = 'login'
      loginForm.username = registerForm.username
      loginForm.password = registerForm.password
      
      // 清空注册表单
      Object.keys(registerForm).forEach(key => {
        if (key !== 'gender') {
          registerForm[key] = ''
        }
      })
      passwordConfirm.value = ''
      
      alert('注册成功，请登录')
    } else {
      registerError.value = res.message || '注册失败，请稍后重试'
    }
  } catch (error) {
    console.error('注册异常:', error)
    registerError.value = error.message || '注册发生异常，请稍后重试'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.auth-card {
  width: 100%;
  max-width: 480px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.auth-header {
  padding: 30px 30px 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.auth-title {
  margin: 0 0 8px;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.auth-subtitle {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.tab {
  flex: 1;
  padding: 16px;
  text-align: center;
  font-weight: 600;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab.active {
  color: #4a6cf7;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #4a6cf7;
  border-radius: 3px 3px 0 0;
}

.tab:hover:not(.active) {
  background-color: #f8f9fa;
}

.form-container {
  padding: 25px 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group.half {
  flex: 1;
  min-width: 0;
}

label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #444;
}

.required {
  color: #e74c3c;
  margin-left: 2px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  color: #aaa;
  font-size: 18px;
}

input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"] {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 15px;
  transition: all 0.3s;
}

input:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.checkbox-label input {
  margin-right: 6px;
}

.forgot-link {
  font-size: 14px;
  color: #4a6cf7;
  text-decoration: none;
  transition: color 0.2s;
}

.forgot-link:hover {
  color: #2b4db1;
  text-decoration: underline;
}

.form-actions {
  margin-bottom: 20px;
}

.btn {
  position: relative;
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
}

.btn-primary:hover {
  background-color: #3a5bd9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 108, 247, 0.2);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-text {
  z-index: 1;
}

.btn-icon {
  margin-left: 8px;
  font-size: 18px;
  transition: transform 0.3s ease;
}

.btn:hover .btn-icon {
  transform: translateX(4px);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-left: 10px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  display: flex;
  align-items: center;
  padding: 12px;
  background-color: rgba(231, 76, 60, 0.1);
  border-radius: 8px;
  color: #e74c3c;
  font-size: 14px;
}

.error-icon {
  margin-right: 8px;
  font-size: 16px;
}

.radio-group {
  display: flex;
  gap: 20px;
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-label input {
  margin-right: 6px;
}

/* 移动设备适配 */
@media (max-width: 576px) {
  .auth-card {
    box-shadow: none;
  }
  
  .form-row {
    flex-direction: column;
    gap: 20px;
  }
  
  .auth-title {
    font-size: 22px;
  }
  
  .auth-subtitle {
    font-size: 14px;
  }
  
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="tel"] {
    font-size: 16px; /* 避免iOS上缩放 */
  }
}
</style> 