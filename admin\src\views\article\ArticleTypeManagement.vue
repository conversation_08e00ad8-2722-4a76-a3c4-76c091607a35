<template>
  <div class="article-type-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="类型名称">
            <el-input v-model="searchForm.typeName" placeholder="请输入政策类型名称" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-container">
          <el-button type="primary" @click="handleAdd">新增类型</el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="typeList"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="typeName" label="类型名称" min-width="200" />
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formType === 'add' ? '新增政策类型' : '编辑政策类型'"
      width="500px"
      @close="closeDialog"
    >
      <el-form
        ref="typeFormRef"
        :model="typeForm"
        :rules="rules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="类型名称" prop="typeName">
          <el-input v-model="typeForm.typeName" placeholder="请输入政策类型名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listArticleTypes, getArticleType, addArticleType, updateArticleType, deleteArticleType } from '@/api/article'

// 加载状态
const tableLoading = ref(false)

// 表单引用，用于表单校验
const typeFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  typeName: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 政策类型列表数据
const typeList = ref([])

// 对话框控制
const dialogVisible = ref(false)
const formType = ref('add') // add: 新增, edit: 编辑

// 表单数据
const typeForm = reactive({
  id: undefined,
  typeName: ''
})

// 表单校验规则
const rules = {
  typeName: [
    { required: true, message: '请输入政策类型名称', trigger: 'blur' },
    { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
  ]
}

// 初始化加载数据
onMounted(() => {
  fetchTypeList()
})

// 获取政策类型列表
const fetchTypeList = async () => {
  tableLoading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchForm
    }
    const res = await listArticleTypes(params)
    // 兼容后端返回的list或records字段
    typeList.value = res.list || res.records || []
    pagination.total = res.total || (res.list ? res.list.length : 0)
  } catch (error) {
    console.error('获取政策类型列表失败:', error)
    ElMessage.error('获取政策类型列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 查询
const handleSearch = () => {
  pagination.current = 1 // 重置到第一页
  fetchTypeList()
}

// 重置搜索
const resetSearch = () => {
  // 重置搜索表单
  searchForm.typeName = ''

  // 重新查询
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchTypeList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  fetchTypeList()
}

// 新增政策类型
const handleAdd = () => {
  formType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑政策类型
const handleEdit = async (row) => {
  formType.value = 'edit'
  resetForm()

  try {
    const res = await getArticleType(row.id)
    Object.assign(typeForm, res)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取政策类型详情失败:', error)
    ElMessage.error('获取政策类型详情失败')
  }
}

// 删除政策类型
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除政策类型"${row.typeName}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deleteArticleType(row.id)
      ElMessage.success('删除成功')
      // 如果删除的是当前页的最后一条数据且不是第一页，则跳转到上一页
      if (typeList.value.length === 1 && pagination.current > 1) {
        pagination.current--
      }
      fetchTypeList()
    } catch (error) {
      console.error('删除政策类型失败:', error)
      ElMessage.error('删除政策类型失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 重置表单
const resetForm = () => {
  if (typeFormRef.value) {
    typeFormRef.value.resetFields()
  }
  // 手动重置表单数据
  Object.assign(typeForm, {
    id: undefined,
    typeName: ''
  })
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!typeFormRef.value) return

  await typeFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formType.value === 'add') {
          // 新增
          await addArticleType(typeForm)
          ElMessage.success('新增成功')
        } else {
          // 编辑
          await updateArticleType(typeForm.id, typeForm)
          ElMessage.success('更新成功')
        }

        // 关闭对话框并刷新列表
        dialogVisible.value = false
        fetchTypeList()
      } catch (error) {
        console.error(formType.value === 'add' ? '新增政策类型失败:' : '更新政策类型失败:', error)
        ElMessage.error(formType.value === 'add' ? '新增政策类型失败' : '更新政策类型失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'

  // 如果是字符串类型，先转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime

  // 如果转换后不是有效日期
  if (isNaN(date.getTime())) return dateTime

  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}
</script>

<style scoped>
.article-type-management-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.search-form {
  flex: 1;
}

.operation-container {
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
