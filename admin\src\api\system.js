import request from '@/utils/request'

// 获取系统配置
export function getSystemConfig() {
  return request({
    url: '/admin/system/config',
    method: 'get'
  })
}

// 更新系统配置
export function updateSystemConfig(data) {
  return request({
    url: '/admin/system/config',
    method: 'put',
    data
  })
}

// 获取当前管理员信息
export function getCurrentAdminInfo() {
  return request({
    url: '/admin/system/profile',
    method: 'get'
  })
}

// 更新管理员个人信息
export function updateAdminProfile(data) {
  return request({
    url: '/admin/system/profile',
    method: 'put',
    data
  })
}

// 修改管理员密码
export function updateAdminPassword(data) {
  return request({
    url: '/admin/system/password',
    method: 'put',
    data
  })
}

// 上传管理员头像
export function uploadAdminAvatar(data) {
  return request({
    url: '/admin/system/avatar',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取系统操作日志
export function getOperationLogs(params) {
  return request({
    url: '/admin/system/logs',
    method: 'get',
    params
  })
}

// 获取系统统计数据
export function getSystemStatistics() {
  return request({
    url: '/admin/system/statistics',
    method: 'get'
  })
} 