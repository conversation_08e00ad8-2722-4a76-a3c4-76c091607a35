<template>
  <div class="article-container">
    <h1 class="page-title">政策信息</h1>
    
    <!-- 搜索栏 -->
    <div class="filter-bar">
      <div class="search-box">
        <input 
          type="text" 
          v-model="searchTitle" 
          placeholder="请输入资讯标题" 
          @keyup.enter="handleSearch"
        />
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="articleList.length === 0" class="empty-container">
      <p>暂无资讯数据</p>
    </div>

    <!-- 资讯列表 -->
    <div v-else class="article-list">
      <router-link 
        v-for="article in articleList" 
        :key="article.id"
        :to="`/policy/${article.id}`"
        class="article-card"
      >
        <!-- 添加图片显示 -->
        <div class="article-image" v-if="article.picture">
          <img :src="article.picture" :alt="article.title" />
        </div>
        <div v-else class="article-image article-image-placeholder">
          <div class="policy-icon">📋</div>
        </div>
        
        <div class="article-card-content">
          <div class="article-type-tag">{{ article.articleType || '创业政策' }}</div>
          <h2 class="article-title">{{ article.title }}</h2>
          <p class="article-summary">{{ article.summary || (article.content && article.content.substring(0, 100)) || '暂无摘要' }}</p>
          <div class="article-meta">
            <span class="article-date">{{ formatDate(article.publishDate || article.createTime) }}</span>
            <span class="article-views">浏览: {{ article.clickNum || 0 }}</span>
          </div>
        </div>
      </router-link>
    </div>

    <!-- 分页控件 -->
    <div class="pagination">
      <button 
        :disabled="currentPage <= 1" 
        @click="changePage(currentPage - 1)" 
        class="page-btn"
      >
        上一页
      </button>
      <span class="page-info">第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</span>
      <button 
        :disabled="currentPage >= totalPages" 
        @click="changePage(currentPage + 1)" 
        class="page-btn"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script>
import { fetchArticleList } from '../api/article'

// 创建格式化日期函数
function formatDate(date) {
  if (!date) return '';
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return date;
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export default {
  name: 'ArticleInfoList',
  data() {
    return {
      articleList: [],
      loading: true,
      error: null,
      searchTitle: '',
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      isFirstLoad: true
    }
  },
  computed: {
    totalPages() {
      return Math.ceil(this.total / this.pageSize) || 1
    }
  },
  created() {
    this.fetchArticles()
  },
  methods: {
    formatDate,
    async fetchArticles() {
      this.loading = true
      try {
        // 输出分页参数，用于调试
        console.log('请求参数:', { pageNum: this.currentPage, pageSize: this.pageSize, title: this.searchTitle })
        
        // 确保pageNum作为current参数传递给后端
        const response = await fetchArticleList(
          this.currentPage,  // 确保这是正确的页码
          this.pageSize, 
          this.searchTitle
        )
        console.log('返回数据:', response) // 打印完整响应，便于调试
        
        if (response.code === 200) {
          if (response.data && Array.isArray(response.data)) {
            // 后端直接返回数组情况的处理
            this.articleList = response.data.slice(0, this.pageSize) // 限制最多显示pageSize条
            this.total = response.data.length
          } else if (response.data && response.data.records) {
            // 标准分页对象格式处理 - 但需要修正可能的数据不一致
            
            // 1. 确保records是一个数组并限制显示数量
            const records = Array.isArray(response.data.records) ? response.data.records : []
            
            // 前端强制验证分页数据
            if (records.length > this.pageSize) {
              console.warn(`后端返回了${records.length}条数据，超过了每页${this.pageSize}条的限制，前端将进行截断处理`);
              this.articleList = records.slice(0, this.pageSize); // 强制限制每页条数
            } else {
              this.articleList = records;
            }
            
            // 2. 修正total值
            if (response.data.total === 0 && records.length > 0) {
              // 如果后端返回的total为0但实际有记录，根据当前记录数估算总数
              // 假设当前是第N页，有M条记录，则估算总数为 (N-1)*pageSize + M
              const estimatedTotal = (this.currentPage - 1) * this.pageSize + records.length
              this.total = estimatedTotal
              console.warn(`后端返回的total为0但有${records.length}条数据，已修正total为${estimatedTotal}`)
            } else {
              // 使用后端返回的total值，如果为0，维持为0
              this.total = response.data.total || 0
            }
            
            // 3. 记录实际获取的记录数（用于调试）
            console.log(`实际返回${records.length}条记录，限制显示${this.articleList.length}条`)
          } else {
            // 处理空数据情况
            this.articleList = []
            this.total = 0
          }
          
          // 4. 确保总页数至少为1页
          const maxPage = Math.max(Math.ceil(this.total / this.pageSize), 1)
          
          // 5. 调整当前页码（如果超出范围）
          if (this.currentPage > maxPage) {
            console.log(`当前页(${this.currentPage})超过最大页(${maxPage})，自动调整`)
            this.currentPage = maxPage
            // 如果当前页被修正并不是第一次加载，重新获取数据
            if (maxPage < this.currentPage && !this.isFirstLoad) {
              this.isFirstLoad = false
              return this.fetchArticles()
            }
          }
          
          // 第一次加载完成后设置标记
          this.isFirstLoad = false
        } else {
          console.error('API返回错误:', response.message)
          this.articleList = []
          this.total = 0
        }
      } catch (err) {
        console.error('获取资讯列表失败:', err)
        this.error = '获取资讯列表失败'
        this.articleList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.currentPage = 1
      this.fetchArticles()
    },
    changePage(page) {
      if (page < 1 || page > this.totalPages) return
      // 确保页码更新
      console.log(`切换页码: 从第${this.currentPage}页到第${page}页`)
      this.currentPage = page
      // 记录旧的数据，用于比较是否真的更新
      const oldArticleIds = this.articleList.map(item => item.id).join(',')
      // 获取新页面数据
      this.fetchArticles().then(() => {
        // 比较新旧数据
        const newArticleIds = this.articleList.map(item => item.id).join(',')
        console.log('页面切换后数据是否变化:', oldArticleIds !== newArticleIds, 
                    '旧IDs:', oldArticleIds, '新IDs:', newArticleIds)
        // 如果数据没变，尝试提醒用户
        if (oldArticleIds === newArticleIds && this.articleList.length > 0) {
          console.warn('警告: 切换页面后数据未发生变化，可能存在分页问题')
        }
      })
    }
  }
}
</script>

<style scoped>
.article-container {
  width: 100%;
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f8f9fa;
}

.page-title {
  font-size: 24px;
  margin-bottom: 20px;
  color: #333;
  text-align: left;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 10px;
}

.search-box {
  display: flex;
  min-width: 150px;
  max-width: 300px;
}

.search-box input {
  flex: 1;
  height: 36px;
  padding: 0 10px;
  border: 1px solid #ddd;
  border-radius: 4px 0 0 4px;
  font-size: 14px;
  min-width: 150px;
  max-width: 200px;
}

.search-btn {
  padding: 0 15px;
  height: 36px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  cursor: pointer;
}

.article-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.article-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  text-decoration: none;
  color: inherit;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.article-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.article-card-content {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-title {
  font-size: 18px;
  font-weight: 600;
  margin: 10px 0;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.article-summary {
  flex: 1;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #999;
}

.loading-container, .empty-container {
  width: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #999;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分页样式 */
.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.page-btn {
  padding: 5px 15px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.page-btn:hover:not(:disabled) {
  background-color: #40a9ff;
}

.page-btn:disabled {
  background-color: #c0c0c0;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .article-list {
    grid-template-columns: 1fr;
  }
  
  .filter-bar {
    flex-direction: column;
  }
  
  .search-box {
    width: 100%;
    max-width: 100%;
  }
  
  .search-box input {
    min-width: 0;
    max-width: none;
  }
}

.article-type-tag {
  display: inline-block;
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  font-weight: 500;
}

.article-views {
  font-size: 12px;
  color: #999;
}

.article-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
  position: relative;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.article-card:hover .article-image img {
  transform: scale(1.05);
}

.article-image-placeholder {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.policy-icon {
  font-size: 36px;
  color: #bdbdbd;
  opacity: 0.8;
}
</style> 