// 文章相关API
import request from '../utils/request'

// 获取文章分页列表
export function fetchArticleList(pageNum = 1, pageSize = 10, title = '', articleType = '') {
  // 确保pageNum和pageSize是数字，并且有合理的默认值
  const parsedPageNum = parseInt(pageNum) || 1;
  const parsedPageSize = parseInt(pageSize) || 10;
  
  // 构建请求参数，确保与后端控制器参数名一致
  const params = { 
    pageNum: parsedPageNum,  // 后端使用pageNum作为页码参数
    pageSize: parsedPageSize
    // 不再添加current参数，避免混淆
  };
  
  if (title) params.title = title;
  if (articleType) params.articleType = articleType;

  console.log('文章列表请求参数:', params);
  
  return request({
    url: '/api/articles',
    method: 'get',
    params
  });
}

// 获取文章详情
export function fetchArticleDetail(id) {
  return request({
    url: `/api/articles/${id}`,
    method: 'get'
  });
}

// 获取文章评论列表
export function fetchArticleComments(articleId) {
  return request({
    url: '/api/article-comments/list',
    method: 'get',
    params: { articleId }
  });
}

// 新增评论
export function addArticleComment(comment) {
  return request({
    url: '/api/article-comments/add',
    method: 'post',
    data: comment
  });
}

// 点赞
export function thumbsUpArticle(id) {
  return request({
    url: `/api/articles/${id}/thumbs-up`,
    method: 'post'
  });
}

// 点踩
export function thumbsDownArticle(id) {
  return request({
    url: `/api/articles/${id}/thumbs-down`,
    method: 'post'
  });
}

// 投票
export function submitArticleVote(vote) {
  const { articleId, ...rest } = vote;
  return request({
    url: `/api/articles/${articleId}/vote`,
    method: 'post',
    data: rest
  });
}
