// 新闻相关API
import request from '../utils/request'

// 获取新闻分页列表
export function fetchNewsList(pageNum = 1, pageSize = 10, title = '') {
  const params = { pageNum, pageSize };
  if (title) params.title = title;
  
  return request({
    url: '/api/news',
    method: 'get',
    params
  });
}

// 获取新闻详情
export function fetchNewsDetail(id) {
  return request({
    url: `/api/news/${id}`,
    method: 'get'
  });
} 