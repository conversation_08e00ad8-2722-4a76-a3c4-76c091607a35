<template>
  <div class="profile-container">
    <div class="profile-content">
      <div class="profile-sidebar">
        <div class="menu">
          <div class="menu-item" :class="{ active: activeTab === 'profile' }" @click="activeTab = 'profile'">
            个人信息
          </div>
          <div class="menu-item" :class="{ active: activeTab === 'favorites' }" @click="activeTab = 'favorites'">
            我的收藏
          </div>
          <div class="menu-item" :class="{ active: activeTab === 'history' }" @click="activeTab = 'history'">
            浏览历史
          </div>
        </div>
      </div>

      <div class="profile-main">
        <!-- 个人信息 -->
        <div v-if="activeTab === 'profile'" class="profile-section">
          <h2 class="section-title">个人信息</h2>

          <div v-if="loading" class="loading">加载中...</div>
          <div v-else>
            <div class="form-group">
              <label>用户名</label>
              <input type="text" v-model="userInfo.username" disabled class="form-input" />
            </div>

            <div class="form-group">
              <label>密码</label>
              <input type="password" v-model="userInfo.password" placeholder="输入新密码以修改" class="form-input" />
            </div>

            <div class="form-group">
              <label>姓名</label>
              <input type="text" v-model="userInfo.fullName" class="form-input" />
            </div>

            <div class="form-group">
              <label>性别</label>
              <div class="radio-group">
                <label class="radio-label">
                  <input type="radio" v-model="userInfo.gender" value="男" /> 男
                </label>
                <label class="radio-label">
                  <input type="radio" v-model="userInfo.gender" value="女" /> 女
                </label>
              </div>
            </div>

            <div class="form-group">
              <label>头像</label>
              <div class="avatar-uploader">
                <img v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar" />
                <div v-else class="avatar-placeholder">
                  <span>请上传</span>
                </div>
                <input type="file" @change="uploadAvatar" accept="image/*" class="file-input" />
              </div>
            </div>

            <div class="form-group">
              <label>邮箱</label>
              <input type="email" v-model="userInfo.email" class="form-input" />
            </div>

            <div class="form-group">
              <label>手机</label>
              <input type="tel" v-model="userInfo.phone" class="form-input" />
            </div>

            <div class="form-actions">
              <button class="btn-primary" @click="updateProfile">更新信息</button>
              <button class="btn-danger" @click="handleLogout">退出登录</button>
            </div>
          </div>
        </div>

        <!-- 我的收藏 -->
        <div v-if="activeTab === 'favorites'" class="profile-section">
          <h2 class="section-title">我的收藏</h2>

          <div v-if="loadingFavorites" class="loading">加载中...</div>
          <div v-else-if="favorites.length === 0" class="no-data">暂无收藏内容</div>
          <div v-else class="favorite-list">
            <div v-for="item in favorites" :key="item.id" class="favorite-item" @click="goToFavorite(item)">
              <div class="favorite-image">
                <img v-if="item.favoritePicture" :src="item.favoritePicture" alt="收藏图片" />
                <div v-else class="no-image">暂无图片</div>
              </div>
              <div class="favorite-info">
                <div class="favorite-name">{{ item.favoriteName }}</div>
                <div class="favorite-meta">
                  <span>收藏于: {{ formatTime(item.createTime) }}</span>
                  <span>{{ getTypeName(item.tableName) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 分页控件 -->
          <div v-if="favorites.length > 0" class="pagination">
            <button
              class="pagination-btn"
              :disabled="favoritesCurrentPage <= 1"
              @click="changeFavoritesPage(favoritesCurrentPage - 1)"
            >
              上一页
            </button>

            <div class="page-info">
              第 <span class="current-page">{{ favoritesCurrentPage }}</span> 页 / 共 {{ favoritesTotalPages }} 页
            </div>

            <button
              class="pagination-btn"
              :disabled="favoritesCurrentPage >= favoritesTotalPages"
              @click="changeFavoritesPage(favoritesCurrentPage + 1)"
            >
              下一页
            </button>
          </div>
        </div>

        <!-- 浏览历史 -->
        <div v-if="activeTab === 'history'" class="profile-section">
          <h2 class="section-title">浏览历史</h2>

          <div v-if="loadingHistory" class="loading">加载中...</div>
          <div v-else-if="history.length === 0" class="no-data">暂无浏览记录</div>
          <div v-else>
            <div class="history-list">
              <div v-for="item in history" :key="item.id" class="history-item" @click="goToHistory(item)">
                <div class="history-info">
                  <div class="history-name">{{ item.name || item.type }}</div>
                  <div class="history-meta">
                    <span>{{ item.type }}</span>
                    <span class="history-time">{{ formatTime(item.createTime) }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 分页控件 -->
            <div v-if="history.length > 0" class="pagination">
              <button
                class="pagination-btn"
                :disabled="historyCurrentPage <= 1"
                @click="changeHistoryPage(historyCurrentPage - 1)"
              >
                上一页
              </button>

              <div class="page-info">
                第 <span class="current-page">{{ historyCurrentPage }}</span> 页 / 共 {{ historyTotalPages }} 页
              </div>

              <button
                class="pagination-btn"
                :disabled="historyCurrentPage >= historyTotalPages"
                @click="changeHistoryPage(historyCurrentPage + 1)"
              >
                下一页
              </button>
            </div>

            <div class="form-actions">
              <button class="btn-primary" @click="handleClearHistory" :disabled="clearingHistory">
                {{ clearingHistory ? '清空中...' : '清空历史记录' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from '../components/NavBar.vue'
import {
  getUserProfile,
  updateUserProfile,
  getUserFavorites,
  logout
} from '../api/user'
import { getBrowseHistoryList, clearBrowseHistory } from '../api/history'

const router = useRouter()
const activeTab = ref('profile')
const loading = ref(false)
const loadingFavorites = ref(false)
const loadingHistory = ref(false)

// 分页相关
const favoritesCurrentPage = ref(1)
const favoritesPageSize = ref(5)
const favoritesTotal = ref(0)
const favoritesTotalPages = computed(() => Math.ceil(favoritesTotal.value / favoritesPageSize.value))

const userInfo = ref({
  id: '',
  username: '',
  password: '',
  fullName: '',
  gender: '男',
  avatar: '',
  email: '',
  phone: ''
})
const favorites = ref<any[]>([])
const history = ref<any[]>([])

// 浏览历史相关
const historyCurrentPage = ref(1)
const historyPageSize = ref(10)
const historyTotal = ref(0)
const historyTotalPages = computed(() => Math.ceil(historyTotal.value / historyPageSize.value))
const clearingHistory = ref(false)

// 上传头像
const uploadAvatar = async (event: any) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件类型和大小
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    alert('头像只能是图片格式!')
    return
  }
  if (!isLt2M) {
    alert('头像图片大小不能超过 2MB!')
    return
  }

  // 创建 FormData
  const formData = new FormData()
  formData.append('file', file)

  try {
    // 上传文件
    const response = await fetch('/uploads/file', {
      method: 'POST',
      body: formData
    })

    const result = await response.json()

    if (result && result.url) {
      // 后端直接返回了name和url，直接使用
      userInfo.value.avatar = result.url
      console.log('头像上传成功:', result.url)
      // 自动更新用户信息，保存头像
      await updateProfile()
    } else if (result.code === 200 && result.data) {
      // 兼容其他可能的响应格式
      userInfo.value.avatar = result.data.url
      await updateProfile()
    } else {
      alert('上传失败：' + (result.message || '未知错误'))
    }
  } catch (error) {
    console.error('上传出错:', error)
    alert('上传出错')
  }
}

// 更新用户信息
const updateProfile = async () => {
  try {
    const res = await updateUserProfile(userInfo.value)

    if (res.code === 200) {
      alert('信息已更新')
    } else {
      alert('更新失败：' + res.message)
    }
  } catch (error) {
    console.error('更新信息出错:', error)
    alert('更新信息出错')
  }
}

// 退出登录
const handleLogout = () => {
  // 先清除所有用户信息
  logout()
  // 提示用户已退出
  alert('您已成功退出登录')
  // 跳转到登录页面而不是首页
  router.push('/login')
}

// 获取用户信息
const fetchUserProfile = async () => {
  loading.value = true
  try {
    // 先检查是否有token，没有则直接跳转到登录页
    const token = localStorage.getItem('token')
    if (!token) {
      alert('请先登录')
      router.push('/login')
      return
    }

    const res = await getUserProfile()

    if (res.code === 200) {
      userInfo.value = res.data
    } else {
      console.error('获取用户信息失败:', res.message)
      // 处理404用户不存在或其他错误情况，跳转到登录页
      if (res.code === 404 || res.code === 401) {
        alert('登录已过期或用户不存在，请重新登录')
        localStorage.removeItem('token') // 清除本地token
        router.push('/login')
      }
    }
  } catch (error) {
    console.error('获取用户信息出错:', error)
    // 发生错误时也跳转登录页
    alert('获取用户信息失败，请重新登录')
    localStorage.removeItem('token')
    router.push('/login')
  } finally {
    loading.value = false
  }
}

// 监听标签页变化
watch(activeTab, (newTab) => {
  if (newTab === 'favorites') {
    fetchFavorites()
  } else if (newTab === 'history') {
    fetchHistory()
  }
})

// 改变收藏分页
const changeFavoritesPage = (page: number) => {
  favoritesCurrentPage.value = page
  fetchFavorites()
}

// 获取收藏列表
const fetchFavorites = async () => {
  loadingFavorites.value = true
  try {
    // 检查token
    const token = localStorage.getItem('token')
    if (!token) {
      alert('请先登录')
      router.push('/login')
      return
    }

    const res = await getUserFavorites({
      pageNum: favoritesCurrentPage.value,
      pageSize: favoritesPageSize.value
    })

    if (res.code === 200) {
      if (res.data && res.data.records) {
        favorites.value = res.data.records || []
        favoritesTotal.value = res.data.total || 0
      } else {
        favorites.value = res.data || []
        // 如果后端没有返回分页信息，则假设当前数据就是全部
        favoritesTotal.value = favorites.value.length
      }
    } else {
      console.error('获取收藏失败:', res.message)
      favorites.value = []

      // 处理未登录或用户不存在情况
      if (res.code === 401 || res.code === 404) {
        alert('登录已过期，请重新登录')
        localStorage.removeItem('token')
        router.push('/login')
      }
    }
  } catch (error) {
    console.error('获取收藏出错:', error)
    favorites.value = []
    alert('获取收藏失败，请重新登录')
    localStorage.removeItem('token')
    router.push('/login')
  } finally {
    loadingFavorites.value = false
  }
}

// 改变历史分页
const changeHistoryPage = (page: number) => {
  historyCurrentPage.value = page
  fetchHistory()
}

// 获取浏览历史
const fetchHistory = async () => {
  loadingHistory.value = true
  try {
    // 检查token
    const token = localStorage.getItem('token')
    if (!token) {
      alert('请先登录')
      router.push('/login')
      return
    }

    const res = await getBrowseHistoryList({
      pageNum: historyCurrentPage.value,
      pageSize: historyPageSize.value
    })

    if (res.code === 200) {
      if (res.data && res.data.records) {
        history.value = res.data.records || []
        historyTotal.value = res.data.total || 0
      } else {
        history.value = res.data || []
        historyTotal.value = history.value.length
      }
    } else {
      console.error('获取浏览历史失败:', res.message)
      history.value = []

      // 处理未登录或用户不存在情况
      if (res.code === 401 || res.code === 404) {
        alert('登录已过期，请重新登录')
        localStorage.removeItem('token')
        router.push('/login')
      }
    }
  } catch (error) {
    console.error('获取浏览历史出错:', error)
    history.value = []
  } finally {
    loadingHistory.value = false
  }
}

// 清空浏览历史
const handleClearHistory = async () => {
  if (!confirm('确定要清空所有浏览历史吗？')) {
    return
  }

  clearingHistory.value = true
  try {
    const res = await clearBrowseHistory()
    if (res.code === 200) {
      alert('浏览历史已清空')
      history.value = []
      historyTotal.value = 0
    } else {
      alert('清空浏览历史失败: ' + res.message)
    }
  } catch (error) {
    console.error('清空浏览历史出错:', error)
    alert('清空浏览历史失败，请稍后重试')
  } finally {
    clearingHistory.value = false
  }
}

// 跳转到收藏详情
const goToFavorite = (item: any) => {
  const routeMap: {[key: string]: string} = {
    'article_info': `/policy/${item.refId}`,
    'news_info': `/news/${item.refId}`,
    'regional_policy': `/region-policy/${item.refId}`,
    'entrepreneurship_forum': `/forum/${item.refId}`
  }

  const route = routeMap[item.tableName]
  if (route) {
    router.push(route)
  }
}

// 跳转到浏览历史
const goToHistory = (item: any) => {
  const routeMap: {[key: string]: string} = {
    'policy_article': `/policy/${item.refid}`,
    'news': `/news/${item.refid}`,
    'region_policy': `/region-policy/${item.refid}`,
    'forum': `/forum/${item.refid}`
  }

  const route = routeMap[item.tableName]
  if (route) {
    router.push(route)
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
}

// 获取类型名称
const getTypeName = (tableName: string) => {
  const typeMap: {[key: string]: string} = {
    'article_info': '政策文章',
    'news_info': '资讯信息',
    'regional_policy': '地区政策',
    'entrepreneurship_forum': '创业论坛'
  }
  return typeMap[tableName] || tableName
}

onMounted(() => {
  // 检查是否已登录
  const token = localStorage.getItem('token')
  if (!token) {
    alert('请先登录')
    router.push('/login')
    return
  }

  fetchUserProfile()
  if (activeTab.value === 'favorites') {
    fetchFavorites()
  }
})
</script>

<style scoped>
.profile-container {
  width: 100%;
  margin: 0;
  padding: 20px;
  min-height: calc(100vh - 60px);
  background-color: #f8f9fa;
}

.profile-content {
  display: flex;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  min-height: calc(100vh - 100px);
  margin-top: 20px;
  overflow: hidden;
}

.profile-sidebar {
  width: 240px;
  flex-shrink: 0;
  border-right: 1px solid #eee;
  background: #f8f9fb;
  border-radius: 12px 0 0 12px;
}

.menu {
  padding-top: 40px;
}

.menu-item {
  padding: 16px 24px;
  font-size: 16px;
  color: #555;
  cursor: pointer;
  transition: all 0.25s ease;
  margin-bottom: 4px;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center;
  gap: 10px;
}

.menu-item:hover {
  background: #f0f4f9;
  color: #2d8cf0;
}

.menu-item.active {
  background: #e6f7ff;
  color: #2d8cf0;
  border-left: 3px solid #2d8cf0;
  font-weight: 500;
}

.menu-icon {
  font-size: 18px;
}

.profile-main {
  flex: 1;
  padding: 40px;
  overflow-x: hidden;
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 36px;
  color: #333;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-group {
  margin-bottom: 24px;
  max-width: 600px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 15px;
  transition: all 0.3s;
  background-color: #fcfcfc;
}

.form-input:focus {
  outline: none;
  border-color: #2d8cf0;
  box-shadow: 0 0 0 2px rgba(45,140,240,0.2);
  background-color: #fff;
}

.form-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.radio-group {
  display: flex;
  gap: 30px;
}

.radio-label {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.avatar-uploader {
  width: 120px;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.avatar-uploader:hover {
  border-color: #2d8cf0;
  background-color: rgba(45,140,240,0.05);
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.form-actions {
  margin-top: 40px;
  display: flex;
  gap: 16px;
}

.btn-primary, .btn-danger {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-primary {
  background: #2d8cf0;
  color: white;
  box-shadow: 0 2px 6px rgba(45,140,240,0.3);
}

.btn-primary:hover {
  background: #2b7ad2;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(45,140,240,0.4);
}

.btn-danger {
  background: #f56c6c;
  color: white;
  box-shadow: 0 2px 6px rgba(245,108,108,0.3);
}

.btn-danger:hover {
  background: #e64242;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(245,108,108,0.4);
}

.loading, .no-data {
  text-align: center;
  padding: 50px 0;
  color: #999;
  font-size: 16px;
}

/* 收藏列表样式 */
.favorite-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 30px;
}

.favorite-item {
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  height: 100%;
  min-height: 220px;
}

.favorite-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.1);
  border-color: #c9e0ff;
}

.favorite-image {
  width: 100%;
  height: 180px;
  overflow: hidden;
}

.favorite-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s;
}

.favorite-item:hover .favorite-image img {
  transform: scale(1.05);
}

.no-image {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.favorite-info {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.favorite-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
  line-height: 1.4;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.favorite-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #888;
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 浏览历史样式 */
.history-list {
  margin-bottom: 20px;
}

.history-item {
  display: flex;
  flex-direction: column;
  padding: 16px 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #f0f0f0;
  text-align: left;
}

.history-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
  border-color: #e0e0e0;
  background-color: #f9f9ff;
}

.history-info {
  flex: 1;
  min-width: 0;
}

.history-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.history-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #888;
  margin-top: 8px;
  border-top: 1px solid #f5f5f5;
  padding-top: 8px;
}

.history-time {
  color: #999;
}

/* 分页控件 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  gap: 20px;
}

.pagination-btn {
  padding: 10px 24px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 15px;
  color: #333;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(0,0,0,0.03);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.pagination-btn:hover:not(:disabled) {
  background: #f5f9ff;
  border-color: #2d8cf0;
  color: #2d8cf0;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 15px;
  color: #666;
  background: #f9f9f9;
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.current-page {
  color: #2d8cf0;
  font-weight: bold;
}

/* 空状态和加载状态 */
.empty-state, .loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #f9f9fb;
  border-radius: 12px;
  margin: 20px 0;
  min-height: 320px;
  border: 1px dashed #e0e0e0;
}

.empty-message, .loading-message {
  margin-top: 20px;
  color: #888;
  font-size: 16px;
  text-align: center;
}

/* 按钮样式优化 */
.btn {
  padding: 12px 24px;
  background: #2d8cf0;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(45,140,240,0.3);
}

.btn:hover {
  background: #2080e4;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(45,140,240,0.4);
}

.btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(45,140,240,0.3);
}

@media (max-width: 768px) {
  .profile-container {
    padding: 10px;
  }

  .profile-content {
    flex-direction: column;
    min-height: auto;
  }

  .profile-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #eee;
    border-radius: 12px 12px 0 0;
  }

  .menu {
    display: flex;
    padding: 10px;
    overflow-x: auto;
    padding-top: 20px;
    -webkit-overflow-scrolling: touch;
  }

  .menu-item {
    padding: 12px 16px;
    white-space: nowrap;
    border-left: none;
    border-bottom: 3px solid transparent;
    margin: 0 5px;
  }

  .menu-item.active {
    border-left: none;
    border-bottom: 3px solid #2d8cf0;
  }

  .profile-main {
    padding: 20px;
  }

  .form-group {
    max-width: 100%;
  }

  .favorite-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .section-title {
    font-size: 20px;
    margin-bottom: 24px;
  }

  .avatar-container {
    margin-bottom: 24px;
  }

  .history-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 16px;
  }

  .history-time {
    min-width: auto;
    width: 100%;
    text-align: left;
    margin-bottom: 8px;
  }

  .btn, .btn-primary, .btn-danger {
    width: 100%;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 10px;
  }

  .pagination-btn {
    flex: 1;
    min-width: 90px;
  }
}

/* 平板电脑响应式布局 */
@media (min-width: 769px) and (max-width: 1024px) {
  .favorite-list {
    grid-template-columns: repeat(2, 1fr);
  }

  .profile-main {
    padding: 30px;
  }

  .profile-sidebar {
    width: 220px;
  }

  .menu-item {
    padding: 14px 20px;
  }

  .form-group {
    max-width: 100%;
  }
}
</style>
