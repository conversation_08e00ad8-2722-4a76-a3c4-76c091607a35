-- ----------------------------
-- Table structure for app_user
-- ----------------------------
DROP TABLE IF EXISTS `app_user`;
CREATE TABLE `app_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `full_name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `gender` varchar(20) DEFAULT NULL COMMENT '性别',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(50) DEFAULT NULL COMMENT '手机号',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMAR<PERSON> KEY (`id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='应用用户表(前端用户)';

-- ----------------------------
-- Records of app_user
-- ----------------------------
INSERT INTO `app_user` VALUES 
(1, '2024-04-01 10:00:00', 'alice', '123456', 'Alice Wang', '女', 'https://img1.com/avatar1.png', '<EMAIL>', '13800000001', '2024-04-01 12:00:00'),
(2, '2024-04-02 09:30:00', 'bob', '123456', 'Bob Li', '男', 'http://localhost:8080/uploads/9293eae4161045c3a68e236726185117.jpg', '<EMAIL>', '13800000002', '2025-04-18 13:31:59'),
(3, '2024-04-03 14:20:00', 'charlie', '123456', 'Charlie Zhang', '男', 'https://img1.com/avatar3.png', '<EMAIL>', '13800000003', '2024-04-03 15:00:00'),
(4, '2024-04-04 08:15:00', 'daisy', '123456', 'Daisy Sun', '女', 'http://localhost:8080/uploads/c7e65b54b88d446687bfb904e7cd2627.jpg', '<EMAIL>', '13800000022', '2024-04-04 09:00:00');