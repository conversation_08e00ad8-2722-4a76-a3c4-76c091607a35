<template>
  <div class="region-policy-container">
    <h1 class="page-title">地区政策查询</h1>
    
    <div class="filter-bar">
      <div class="filter-group">
        <div class="filter-item">
          <label>选择地区：</label>
          <select v-model="selectedRegion" @change="handleFilterChange">
            <option value="">全部</option>
            <option v-for="region in regionList" :key="region">{{ region }}</option>
          </select>
        </div>
        
        <div class="filter-item">
          <label>政策标题：</label>
          <div class="search-box">
            <input 
              type="text" 
              v-model="searchTitle" 
              placeholder="输入政策名称关键词"
              @keyup.enter="handleFilterChange"
            />
            <button class="search-btn" @click="handleFilterChange">搜索</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div>加载数据中...</div>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">!</div>
      <div class="error-message">{{ error }}</div>
      <button class="retry-button" @click="fetchPolicyList">重试</button>
    </div>
    
    <!-- 数据为空状态 -->
    <div v-else-if="policyList.length === 0" class="empty-container">
      <div class="empty-icon">📋</div>
      <div class="empty-text">暂无符合条件的政策信息</div>
      <div class="empty-tips">您可以尝试更换筛选条件或清除筛选条件后重试</div>
      <button class="clear-filter-btn" @click="clearFilters">清除筛选条件</button>
    </div>
    
    <!-- 政策列表 -->
    <div v-else class="policy-grid">
      <div 
        v-for="item in policyList" 
        :key="item.id" 
        class="policy-card" 
        @click="goDetail(item.id)"
      >
        <div class="card-header">
          <div class="card-type">{{ item.policyType || '一般政策' }}</div>
          <div class="card-region">{{ item.regionName }}</div>
        </div>
        <h3 class="card-title">{{ item.title }}</h3>
        <div class="card-content">
          <div class="card-row"><span class="field-label">有效期：</span>{{ item.validPeriod || '无' }}</div>
          <div class="card-row"><span class="field-label">支持金额：</span>{{ item.supportAmount || '无' }}</div>
          <div class="card-row"><span class="field-label">联系电话：</span>{{ item.contactPhone || '无' }}</div>
          <div class="card-description">
            {{ item.content ? (item.content.replace(/<[^>]+>/g, '').slice(0, 100) + '...') : '暂无详细描述' }}
          </div>
        </div>
        <div class="card-footer">
          <div class="publish-time">发布时间: {{ formatDate(item.publishTime) }}</div>
          <div class="view-detail-btn">查看详情 →</div>
        </div>
      </div>
    </div>
    
    <!-- 分页控件 -->
    <div v-if="policyList.length > 0" class="pagination">
      <button 
        class="pagination-btn" 
        :disabled="currentPage <= 1" 
        @click="changePage(currentPage - 1)"
      >
        上一页
      </button>
      
      <div class="page-info">
        第 <span class="current-page">{{ currentPage }}</span> 页 / 共 {{ totalPages }} 页
      </div>
      
      <button 
        class="pagination-btn" 
        :disabled="currentPage >= totalPages" 
        @click="changePage(currentPage + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { fetchRegionPolicyList, fetchRegionList as fetchRegionListApi } from '../api/regionPolicy'

const router = useRouter()
const regionList = ref<string[]>([])
const selectedRegion = ref('')
const searchTitle = ref('')
const policyList = ref<any[]>([])
const loading = ref(false)
const error = ref('')
const isFirstLoad = ref(true)

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '无'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-')
  } catch (e) {
    return dateString
  }
}

const fetchRegionList = async () => {
  try {
    const res = await fetchRegionListApi()
    if (res.code === 200) {
      regionList.value = res.data
    } else {
      console.error('获取地区列表失败:', res.message)
    }
  } catch (err) {
    console.error('获取地区列表出错:', err)
  }
}

const fetchPolicyList = async () => {
  loading.value = true
  error.value = ''
  
  try {
    console.log('地区政策查询参数:', { 
      pageNum: currentPage.value, 
      pageSize: pageSize.value, 
      regionName: selectedRegion.value,
      title: searchTitle.value
    })
    
    const res = await fetchRegionPolicyList({ 
      pageNum: currentPage.value, 
      pageSize: pageSize.value, 
      regionName: selectedRegion.value,
      title: searchTitle.value
    })
    
    console.log('地区政策返回数据:', res)
    
    if (res.code === 200) {
      if (res.data && Array.isArray(res.data)) {
        // 后端直接返回数组情况的处理
        policyList.value = res.data.slice(0, pageSize.value) // 限制最多显示pageSize条
        total.value = res.data.length
      } else if (res.data && res.data.records && Array.isArray(res.data.records)) {
        // 标准分页对象格式处理 - 但需要修正可能的数据不一致
        
        // 1. 确保records是一个数组并限制显示数量
        const records = res.data.records.filter(item => !!item) // 过滤无效数据
        policyList.value = records.slice(0, pageSize.value) // 强制限制每页条数
        
        // 2. 修正total值
        if (res.data.total === 0 && records.length > 0) {
          // 如果后端返回的total为0但实际有记录，根据当前记录数估算总数
          const estimatedTotal = (currentPage.value - 1) * pageSize.value + records.length
          total.value = estimatedTotal
          console.warn(`后端返回的total为0但有${records.length}条数据，已修正total为${estimatedTotal}`)
        } else {
          // 使用后端返回的total值
          total.value = res.data.total || 0
        }
        
        // 3. 记录实际获取的记录数（用于调试）
        console.log(`实际返回${records.length}条记录，限制显示${policyList.value.length}条`)
      } else {
        console.warn('返回数据格式不符合预期:', res)
        policyList.value = []
        total.value = 0
      }
      
      // 4. 确保总页数至少为1页
      const maxPage = Math.max(Math.ceil(total.value / pageSize.value), 1)
      
      // 5. 调整当前页码（如果超出范围）
      if (currentPage.value > maxPage) {
        console.log(`当前页(${currentPage.value})超过最大页(${maxPage})，自动调整`)
        currentPage.value = maxPage
        // 如果当前页被修正并不是第一次加载，重新获取数据
        if (maxPage < currentPage.value && !isFirstLoad.value) {
          return fetchPolicyList()
        }
      }
      
      // 第一次加载完成后设置标记
      isFirstLoad.value = false
    } else {
      console.error('获取政策列表失败:', res.message)
      policyList.value = []
      total.value = 0
      error.value = res.message || '获取政策列表失败'
    }
  } catch (err: any) {
    console.error('获取政策列表出错:', err)
    policyList.value = []
    total.value = 0
    error.value = err?.message || '获取数据发生异常'
  } finally {
    loading.value = false
  }
}

const handleFilterChange = () => {
  currentPage.value = 1 // 重置为第一页
  fetchPolicyList()
}

const clearFilters = () => {
  selectedRegion.value = ''
  searchTitle.value = ''
  currentPage.value = 1
  fetchPolicyList()
}

const changePage = (page: number) => {
  currentPage.value = page
  fetchPolicyList()
}

const goDetail = (id: number) => {
  router.push(`/region-policy/${id}`)
}

onMounted(() => {
  fetchRegionList()
  fetchPolicyList()
})
</script>

<style scoped>
.region-policy-container {
  width: 100%;
  padding: 24px;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.page-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.filter-bar {
  margin-bottom: 24px;
  display: flex;
  background: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 减小间隔 */
  width: 100%;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 20px; /* 添加右侧间距 */
}

.filter-bar label {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
}

.filter-bar select {
  width: auto;
  min-width: 150px;
  max-width: 200px;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}

.filter-bar input {
  width: auto;
  min-width: 150px;
  max-width: 200px;
  height: 36px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0 12px;
  font-size: 14px;
  color: #333;
}

.filter-bar select:focus,
.filter-bar input:focus {
  border-color: #1976d2;
  outline: none;
}

.search-box {
  display: flex;
  width: auto;
  gap: 8px;
}

.search-btn {
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0 16px;
  cursor: pointer;
  height: 36px;
  font-size: 14px;
}

.policy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  width: 100%;
}

.policy-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.policy-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f3f7ff;
  border-bottom: 1px solid #e8f0fe;
}

.card-type {
  font-size: 13px;
  color: #1976d2;
  font-weight: 500;
  background: rgba(25, 118, 210, 0.08);
  padding: 2px 8px;
  border-radius: 12px;
}

.card-region {
  font-size: 13px;
  color: #666;
}

.card-title {
  padding: 16px 16px 8px;
  margin: 0;
  font-size: 17px;
  color: #333;
  line-height: 1.4;
}

.card-content {
  padding: 0 16px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-row {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.field-label {
  color: #888;
  margin-right: 4px;
}

.card-description {
  margin-top: 12px;
  font-size: 14px;
  color: #555;
  line-height: 1.6;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #eee;
  background: #fafafa;
}

.publish-time {
  font-size: 13px;
  color: #999;
}

.view-detail-btn {
  font-size: 14px;
  color: #1976d2;
  font-weight: 500;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 32px;
  gap: 16px;
  margin-bottom: 32px; /* 添加底部间距 */
}

.pagination-btn {
  padding: 8px 16px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.pagination-btn:hover:not(:disabled) {
  background: #e0e0e0;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 14px;
  color: #666;
}

.current-page {
  color: #1976d2;
  font-weight: bold;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
  text-align: center;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(25, 118, 210, 0.2);
  border-top-color: #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: #f44336;
  color: white;
  font-size: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.error-message {
  color: #f44336;
  font-size: 16px;
  margin-bottom: 16px;
}

.retry-button,
.clear-filter-btn {
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ccc;
}

.empty-text {
  font-size: 18px;
  color: #666;
  margin-bottom: 8px;
}

.empty-tips {
  font-size: 14px;
  color: #999;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .policy-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .filter-item {
    min-width: unset;
    margin-right: 0;
    width: 100%;
  }
  
  .region-policy-container {
    padding: 16px;
  }
  
  .filter-group {
    flex-direction: column;
  }
}
</style> 