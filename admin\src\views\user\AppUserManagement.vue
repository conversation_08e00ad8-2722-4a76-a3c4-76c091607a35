<template>
  <div class="app-user-container">
    <div class="search-box">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input v-model="queryParams.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="queryParams.phone" placeholder="请输入手机号" clearable></el-input>
        </el-form-item>
        <!-- <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <el-table v-loading="loading" :data="userList" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="60"></el-table-column>
        <el-table-column prop="username" label="用户名" min-width="30"></el-table-column>
        <el-table-column prop="avatar" label="头像" width="80">
          <template #default="scope">
            <el-image 
              style="width: 40px; height: 40px; border-radius: 50%;"
              :src="scope.row.avatar || defaultAvatar" 
              :preview-src-list="[scope.row.avatar || defaultAvatar]" 
              fit="cover"
              preview-teleported
            >
              <template #error>
                <div class="image-slot">
                  <el-icon><icon-picture /></el-icon>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="fullName" label="姓名" min-width="30"></el-table-column>
        <el-table-column prop="gender" label="性别" width="60"></el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="30"></el-table-column>
        <el-table-column prop="phone" label="手机号" width="115"></el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="210" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleResetPassword(scope.row)">重置密码</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 编辑用户对话框 -->
    <el-dialog 
      title="编辑用户信息" 
      v-model="dialogVisible" 
      width="500px"
      :close-on-click-modal="false"
      @close="resetForm"
    >
      <el-form :model="userForm" :rules="rules" ref="userFormRef" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" disabled></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="fullName">
          <el-input v-model="userForm.fullName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="userForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
            <el-radio label="未知">未知</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="头像" prop="avatar">
          <el-upload
            class="avatar-uploader"
            action="/api/uploads/file"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="userForm.avatar" :src="userForm.avatar" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox, ElImage, ElIcon, ElUpload } from 'element-plus'
import { Picture as IconPicture, Plus } from '@element-plus/icons-vue'
import {
  listAppUsers,
  getAppUser,
  updateAppUser,
  deleteAppUser,
  updateAppUserStatus,
  resetAppUserPassword
} from '@/api/user'

// 默认头像URL (如果需要)
const defaultAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png' // Element Plus 默认头像或你的自定义图片URL

export default {
  name: 'AppUserManagement',
  components: { IconPicture, Plus },
  setup() {
    const userFormRef = ref(null)
    
    const state = reactive({
      // 加载状态
      loading: false,
      // 用户列表
      userList: [],
      // 总数
      total: 0,
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        username: '',
        phone: '',
        // status: '' // 移除 status 属性
      },
      // 对话框可见性
      dialogVisible: false,
      // 表单对象
      userForm: {
        id: undefined,
        username: '',
        fullName: '',
        gender: '未知',
        email: '',
        phone: '',
        avatar: ''
      },
      // 表单校验规则
      rules: {
        fullName: [
          { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        avatar: [
          // { type: 'url', message: '请输入有效的URL', trigger: 'blur' } // 可选的URL校验
        ]
      }
    })

    // 获取用户列表
    const getList = async () => {
      state.loading = true
      try {
        const res = await listAppUsers(state.queryParams)
        state.userList = res.list || []
        state.total = res.total || 0
      } catch (error) {
        console.error('获取用户列表失败', error)
        ElMessage.error(error.message || '获取用户列表失败')
        state.userList = []
        state.total = 0
      } finally {
        state.loading = false
      }
    }

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1
      getList()
    }

    // 重置查询条件
    const resetQuery = () => {
      state.queryParams.username = ''
      state.queryParams.phone = ''
      // state.queryParams.status = '' // 移除重置 status 的代码
      handleQuery()
    }

    // 每页数量改变事件
    const handleSizeChange = (val) => {
      state.queryParams.limit = val
      getList()
    }

    // 当前页改变事件
    const handleCurrentChange = (val) => {
      state.queryParams.page = val
      getList()
    }

    // 编辑用户按钮点击事件
    const handleEdit = async (row) => {
      resetForm();
      try {
        state.userForm = { 
          id: row.id, 
          username: row.username, 
          fullName: row.fullName || '',
          gender: row.gender || '未知',
          email: row.email || '',
          phone: row.phone || '',
          avatar: row.avatar || ''
        } 
        state.dialogVisible = true
      } catch (error) {
        console.error('准备编辑用户失败', error)
        ElMessage.error('加载用户信息失败')
      }
    }

    // 重置密码按钮点击事件
    const handleResetPassword = (row) => {
      ElMessageBox.confirm(
        `确定要重置 ${row.username} 的密码吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await resetAppUserPassword(row.id)
          ElMessage.success('密码重置成功')
        } catch (error) {
          console.error('密码重置失败', error)
        }
      }).catch(() => {})
    }

    // 删除按钮点击事件
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除用户"${row.username}"吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteAppUser(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          console.error('删除失败', error)
        }
      }).catch(() => {})
    }

    // 重置表单方法
    const resetForm = () => {
      if (userFormRef.value) {
        userFormRef.value.resetFields();
      }
      state.userForm = {
        id: undefined,
        username: '',
        fullName: '',
        gender: '未知',
        email: '',
        phone: '',
        avatar: ''
      };
    };

    // 提交表单
    const submitForm = () => {
      userFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            await updateAppUser(state.userForm.id, state.userForm)
            ElMessage.success('更新成功')
            state.dialogVisible = false
            getList()
          } catch (error) {
            console.error('提交表单失败', error)
          }
        }
      })
    }

    // 格式化日期时间
    const formatDateTime = (dateTime) => {
      if (!dateTime) return '--'
      const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
      if (isNaN(date.getTime())) return dateTime
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }

    // 头像上传成功回调
    const handleAvatarSuccess = (res, file) => {
      // 假设后端返回的数据结构中包含 url 字段
      // 需要根据实际后端返回结构调整 res.url 或 res.data.url
      state.userForm.avatar = res.url || (res.data && res.data.url) || ''
      // 清除可能存在的校验错误提示
      if (userFormRef.value) {
        userFormRef.value.clearValidate('avatar');
      }
    }

    // 头像上传前检查
    const beforeAvatarUpload = (file) => {
      const isImage = file.type.startsWith('image/') // 检查是否为图片类型
      const isLt2M = file.size / 1024 / 1024 < 2 // 检查大小是否小于 2MB

      if (!isImage) {
        ElMessage.error('头像只能是图片格式!')
      }
      if (!isLt2M) {
        ElMessage.error('头像图片大小不能超过 2MB!')
      }
      return isImage && isLt2M // 都满足才允许上传
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state),
      userFormRef,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleEdit,
      handleResetPassword,
      handleDelete,
      submitForm,
      resetForm,
      formatDateTime,
      defaultAvatar,
      handleAvatarSuccess,
      beforeAvatarUpload
    }
  }
}
</script>

<style scoped>
.app-user-container {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-box {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-box {
  margin-top: 15px;
  text-align: right;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px; /* Adjust size if needed */
}
.image-slot .el-icon {
  font-size: 20px; /* Adjust icon size if needed */
}

/* 头像上传样式 (参考 ArticleManagement.vue) */
.avatar-uploader .avatar {
  width: 100px; /* 调整尺寸 */
  height: 100px;
  display: block;
  object-fit: cover;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}
.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}
.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px; /* 调整尺寸 */
  height: 100px;
  text-align: center;
}
</style> 