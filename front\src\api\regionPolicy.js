import request from '../utils/request'

// 获取区域政策列表
export function fetchRegionPolicyList({ pageNum = 1, pageSize = 10, regionName = '', title = '' }) {
  // 确保分页参数为数字
  const parsedPageNum = parseInt(pageNum) || 1;
  const parsedPageSize = parseInt(pageSize) || 10;
  
  const params = {
    pageNum: parsedPageNum,
    pageSize: parsedPageSize,
    current: parsedPageNum, // 兼容后端使用current参数
    regionName,
    title
  };
  
  console.log('地区政策请求参数:', params);
  
  return request({
    url: '/api/region-policy',
    method: 'get',
    params
  });
}

// 获取区域列表
export function fetchRegionList() {
  return request({
    url: '/api/region-policy/regions',
    method: 'get'
  });
}

// 获取区域政策详情
export function fetchRegionPolicyDetail(id) {
  if (!id) {
    console.error('获取地区政策详情缺少ID参数');
    return Promise.reject(new Error('缺少ID参数'));
  }
  
  return request({
    url: `/api/region-policy/${id}`,
    method: 'get'
  });
} 