{"name": "front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@types/node": "^22.15.3", "@vitejs/plugin-vue": "^5.2.3", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.10"}, "dependencies": {"axios": "^1.8.4", "element-plus": "^2.9.7", "swiper": "^11.2.6", "vue-router": "^4.5.0"}}