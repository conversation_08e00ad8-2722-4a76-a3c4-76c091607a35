import request from '@/utils/request'

// 分页查询投票列表
export function listVotes(params) {
  return request({
    url: '/admin/votes',
    method: 'get',
    params
  })
}

// 根据ID获取投票详情
export function getVote(id) {
  return request({
    url: `/admin/votes/${id}`,
    method: 'get'
  })
}

// 新增投票
export function addVote(data) {
  return request({
    url: '/admin/votes',
    method: 'post',
    data
  })
}

// 更新投票
export function updateVote(id, data) {
  return request({
    url: `/admin/votes/${id}`,
    method: 'put',
    data
  })
}

// 删除投票
export function deleteVote(id) {
  return request({
    url: `/admin/votes/${id}`,
    method: 'delete'
  })
}

// 获取投票统计数据
export function getVoteStatistics(id) {
  return request({
    url: `/admin/votes/${id}/statistics`,
    method: 'get'
  })
}

// 更新投票状态（开始/暂停/结束）
export function updateVoteStatus(id, status) {
  return request({
    url: `/admin/votes/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取投票选项列表
export function getVoteOptions(voteId) {
  return request({
    url: `/admin/votes/${voteId}/options`,
    method: 'get'
  })
}

// 添加投票选项
export function addVoteOption(voteId, data) {
  return request({
    url: `/admin/votes/${voteId}/options`,
    method: 'post',
    data
  })
}

// 删除投票选项
export function deleteVoteOption(voteId, optionId) {
  return request({
    url: `/admin/votes/${voteId}/options/${optionId}`,
    method: 'delete'
  })
} 