/**
 * 浏览历史API类型声明
 */

/**
 * 浏览历史数据结构
 */
export interface BrowseHistory {
  id?: number;
  userId?: number;
  username?: string;
  tableName: string;
  name?: string;
  picture?: string;
  type: string;
  inteltime?: string;
  refid?: number | string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 记录用户浏览历史
 * @param data 浏览记录信息
 * @returns 操作结果
 */
export function recordBrowseHistory(data: BrowseHistory): Promise<any>;

/**
 * 获取用户浏览历史列表
 * @param params 查询参数
 * @returns 浏览历史列表
 */
export function getBrowseHistoryList(params: {
  pageNum?: number;
  pageSize?: number;
}): Promise<any>;

/**
 * 删除浏览历史记录
 * @param id 历史记录ID
 * @returns 操作结果
 */
export function deleteBrowseHistory(id: number): Promise<any>;

/**
 * 清空所有浏览历史
 * @returns 操作结果
 */
export function clearBrowseHistory(): Promise<any>; 