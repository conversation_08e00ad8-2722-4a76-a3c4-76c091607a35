/**
 * 认证状态管理工具
 */
import { getUserProfile } from '../api/user'

// token键名
const TOKEN_KEY = 'token'
const USERNAME_KEY = 'username'
const REMEMBER_KEY = 'rememberMe'

/**
 * 获取token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * 保存token
 * @param {string} token 
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * 检查是否登录
 */
export function isLoggedIn() {
  return !!getToken()
}

/**
 * 保存"记住我"设置和用户名
 * @param {string} username 用户名
 * @param {boolean} remember 是否记住
 */
export function saveLoginInfo(username, remember) {
  if (remember) {
    localStorage.setItem(REMEMBER_KEY, 'true')
    localStorage.setItem(USERNAME_KEY, username)
  } else {
    localStorage.removeItem(REMEMBER_KEY)
    localStorage.removeItem(USERNAME_KEY)
  }
}

/**
 * 获取用户信息并验证登录状态
 * @returns {Promise<Object>} 用户信息
 */
export async function checkLoginStatus() {
  // 检查token
  if (!isLoggedIn()) {
    return { loggedIn: false }
  }
  
  try {
    // 请求用户信息
    const res = await getUserProfile()
    console.log('验证登录状态:', res)
    
    // 验证响应数据
    if (res) {
      let user = null
      
      if (res.code === 200 && res.data) {
        // 标准响应格式
        user = res.data
      } else if (res.id || res.username) {
        // 直接返回用户信息
        user = res
      }
      
      if (user) {
        // 如果用户缺少名称，尝试从localStorage获取
        if (!user.username) {
          user.username = localStorage.getItem(USERNAME_KEY) || '用户'
        }
        return { loggedIn: true, user }
      }
    }
    
    // 验证失败，清理token
    removeToken()
    return { loggedIn: false, error: '会话已过期' }
  } catch (err) {
    console.error('验证登录状态出错:', err)
    // 出错但不一定要清除token，可能是网络问题
    return { 
      loggedIn: true, 
      error: '获取用户信息失败',
      user: {
        username: localStorage.getItem(USERNAME_KEY) || '用户',
        fullName: ''
      }
    }
  }
}

/**
 * 清除用户登录信息，完全登出
 */
export function clearLoginState() {
  removeToken();
  localStorage.removeItem(USERNAME_KEY);
  localStorage.removeItem(REMEMBER_KEY);
}

/**
 * 验证token是否有效
 * @returns {boolean} token是否有效
 */
export function validateToken() {
  const token = getToken();
  if (!token) {
    return false;
  }
  
  // 简单验证token是否过期（如果使用JWT可以解析token检查exp字段）
  // 这里简单检查token是否存在
  return !!token;
}

/**
 * 退出登录，清除所有认证相关信息
 */
export function logout() {
  // 清除token
  removeToken();
  
  // 清除localStorage中保存的所有用户相关信息
  localStorage.removeItem('userInfo');
  localStorage.removeItem(USERNAME_KEY);
  localStorage.removeItem(REMEMBER_KEY);
  
  console.log('用户已完全登出，所有身份信息已清除');
} 