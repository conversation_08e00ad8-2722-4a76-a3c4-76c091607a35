<template>
  <div class="article-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <p>{{ error }}</p>
      <button @click="loadArticleDetail" class="reload-btn">重新加载</button>
    </div>

    <!-- 文章内容 -->
    <div v-else-if="detail" class="article-container">
      <div class="article-header">
        <h1 class="article-title">{{ detail.title }}</h1>
        <div class="article-meta">
          <div class="meta-left">
            <span class="article-type">{{ detail.articleType || '创业政策' }}</span>
            <div class="article-time">发布时间：{{ formatDate(detail.createTime) }}</div>
          </div>
          <div class="article-views">浏览数：{{ detail.clickNum || 0 }}</div>
        </div>
      </div>
      <div v-if="detail.picture" class="article-image">
        <img :src="detail.picture" alt="封面" />
      </div>
      <div class="article-content">
        <p v-if="detail.summary">{{ detail.summary }}</p>
        <span v-if="detail.content" v-html="detail.content"></span>
      </div>
      <div class="article-actions">
        <span @click="doThumbsUp" class="action-btn">👍 {{ detail.thumbsUpNum || 0 }}</span>
        <span @click="doThumbsDown" class="action-btn">👎 {{ detail.thumbsDownNum || 0 }}</span>
        <span @click="doFavorite" class="action-btn">⭐ 收藏</span>
        <button v-if="isLoggedIn" @click="showVote = true" class="action-btn">投票</button>
        <span class="vote-count">票数：{{ detail.voteCount || 0 }}</span>
      </div>
    </div>

    <!-- 投票弹窗 -->
    <div v-if="showVote && detail" class="vote-dialog-mask">
      <div class="vote-dialog">
        <div class="vote-title">投票</div>
        <form @submit.prevent="submitVote">
          <div class="vote-row"><span>标题：</span>{{ detail.title }}</div>
          <div class="vote-row"><span>类型：</span>{{ detail.articleType }}</div>
          <div class="vote-row"><span>票数：</span><input v-model.number="voteCount" type="number" min="1" /></div>
          <div class="vote-row"><span>投票时间：</span><input v-model="voteDate" type="date" /></div>
          <div class="vote-actions">
            <button type="submit" class="vote-submit">提交</button>
            <button type="button" class="vote-cancel" @click="showVote = false">取消</button>
          </div>
        </form>
      </div>
    </div>

    <!-- 评论区 -->
    <div v-if="detail" class="comment-section">
      <h3 class="section-title">评论交流
        <span class="comment-count" v-if="commentList && commentList.length > 0">
          ({{ commentList.length }})
        </span>
      </h3>
      
      <div class="comment-area">
        <textarea
          v-model="commentText"
          rows="3"
          placeholder="分享您对这篇文章的看法..."
          :disabled="!isLoggedIn"
          class="comment-input"
        ></textarea>
        <div class="comment-controls">
          <span v-if="!isLoggedIn" class="login-tip">需要登录后才能评论</span>
          <button
            @click="submitComment"
            :disabled="!commentText.trim() || !isLoggedIn"
            class="submit-btn"
          >
            发表评论
          </button>
        </div>
      </div>
      
      <div class="comment-list-container">
        <div v-if="commentList.length === 0" class="no-comment">暂无评论，快来发表您的看法吧！</div>
        <ul v-else class="comment-list">
          <li v-for="comment in commentList" :key="comment.id" class="comment-item">
            <div class="comment-header">
              <span class="nickname">{{ comment.nickname }}</span>
              <span class="comment-time">{{ formatDate(comment.createTime) }}</span>
            </div>
            <div class="comment-content">{{ comment.content }}</div>
            <div v-if="comment.replyContent" class="reply-content">管理员回复：{{ comment.replyContent }}</div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { fetchArticleDetail, fetchArticleComments, addArticleComment, thumbsUpArticle, thumbsDownArticle, submitArticleVote } from '../api/article'
import { fetchUserByUsername } from '../api/user'
import { recordHistory } from '../utils/history'
import request from '../utils/request'

const route = useRoute()
const detail = ref<any>(null)
const commentList = ref<any[]>([])
const commentText = ref('')
const isLoggedIn = computed(() => !!localStorage.getItem('token'))
const loading = ref(true)
const error = ref('')

// 投票弹窗相关
const showVote = ref(false)
const voteCount = ref(1)
const voteDate = ref(new Date().toISOString().slice(0, 10))

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return '';
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return date;
  
  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, '0');
  const day = String(dateObj.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 加载文章详情
const loadArticleDetail = async () => {
  if (!route.params.id) {
    error.value = '缺少文章ID';
    loading.value = false;
    return;
  }

  loading.value = true;
  error.value = '';
  
  try {
    const res = await fetchArticleDetail(route.params.id);
    
    if (res.code === 200 && res.data) {
      detail.value = res.data;
      
      // 记录浏览历史
      if (isLoggedIn.value && detail.value) {
        const historyData = {
          tableName: 'policy_article',
          refid: route.params.id,
          type: '政策信息',
          name: detail.value.title,
          picture: detail.value.picture || ''
        }
        try {
          recordHistory(route, historyData);
        } catch (historyError) {
          console.error('记录历史失败:', historyError);
        }
      }
      
      fetchComments();
    } else {
      error.value = res.message || '获取文章详情失败';
      detail.value = null;
    }
  } catch (err) {
    console.error('获取文章详情失败:', err);
    error.value = '获取文章详情时发生错误';
    detail.value = null;
  } finally {
    loading.value = false;
  }
}

const fetchComments = async () => {
  try {
    if (!route.params.id) return;
    
    const res = await fetchArticleComments(route.params.id);
    if (res.code === 200) {
      commentList.value = Array.isArray(res.data) ? res.data : [];
    } else {
      commentList.value = [];
    }
  } catch (err) {
    console.error('获取评论失败:', err);
    commentList.value = [];
  }
}

const submitComment = async () => {
  if (!commentText.value.trim()) {
    alert('请输入评论内容');
    return;
  }
  
  if (!detail.value || !detail.value.id) {
    alert('文章信息不完整，无法评论');
    return;
  }
  
  try {
    const comment = {
      articleId: detail.value.id,
      content: commentText.value,
      nickname: localStorage.getItem('nickname') || '匿名用户',
    };
    
    const res = await addArticleComment(comment);
    if (res.code === 200) {
      commentText.value = '';
      fetchComments();
      alert('评论成功！');
    } else {
      alert('评论失败: ' + (res.message || '未知错误'));
    }
  } catch (err) {
    console.error('提交评论失败:', err);
    alert('评论失败，请稍后再试');
  }
}

// 点赞/点踩功能
const doThumbsUp = async () => {
  if (!detail.value || !detail.value.id) return;
  
  try {
    const res = await thumbsUpArticle(detail.value.id);
    if (res.code === 200) {
      detail.value.thumbsUpNum = (detail.value.thumbsUpNum || 0) + 1;
    } else {
      alert('点赞失败: ' + (res.message || '未知错误'));
    }
  } catch (err) {
    console.error('点赞失败:', err);
    alert('点赞失败，请稍后再试');
  }
}

const doThumbsDown = async () => {
  if (!detail.value || !detail.value.id) return;
  
  try {
    const res = await thumbsDownArticle(detail.value.id);
    if (res.code === 200) {
      detail.value.thumbsDownNum = (detail.value.thumbsDownNum || 0) + 1;
    } else {
      alert('点踩失败: ' + (res.message || '未知错误'));
    }
  } catch (err) {
    console.error('点踩失败:', err);
    alert('点踩失败，请稍后再试');
  }
}

// 收藏功能
const doFavorite = async () => {
  if (!detail.value || !detail.value.id) return;
  try {
    const res = await request({
      url: `/api/articles/${detail.value.id}/favorite`,
      method: 'post'
    });
    if (res.code === 200 && res.data) {
      alert('收藏成功！');
    } else {
      alert('收藏失败: ' + (res.message || '未知错误'));
    }
  } catch (err) {
    console.error('收藏失败:', err);
    alert('收藏失败，请稍后再试');
  }
}

// 投票功能（对接后端投票接口）
const submitVote = async () => {
  if (!detail.value) return;
  try {
    const voteData = {
      articleId: detail.value.id,
      voteCount: voteCount.value,
      voteDate: voteDate.value,
      title: detail.value.title,
      articleType: detail.value.articleType,
      userId: localStorage.getItem('userId') || '',
      nickname: localStorage.getItem('nickname') || '匿名用户',
    };
    const res = await submitArticleVote(voteData);
    if (res.code === 200) {
      alert('投票成功！');
      detail.value.voteCount = (detail.value.voteCount || 0) + voteCount.value;
      showVote.value = false;
    } else {
      alert('投票失败: ' + (res.message || '未知错误'));
    }
  } catch (err) {
    console.error('投票失败:', err);
    alert('投票失败，请稍后再试');
  }
}

// 页面加载时获取文章详情
onMounted(loadArticleDetail);
</script>

<style scoped>
.article-detail {
  width: 100%;
  min-height: calc(100vh - 60px);
  padding: 30px;
  background-color: #f8f9fa;
}

.article-container {
  max-width: 900px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.article-header {
  padding: 40px 60px 30px;
  border-bottom: 1px solid #f0f0f0;
}

.article-title {
  font-size: 32px;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
  line-height: 1.4;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  color: #666;
  font-size: 15px;
}

.meta-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.article-type {
  padding: 6px 16px;
  background-color: rgba(45, 140, 240, 0.1);
  color: #2d8cf0;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.article-time, .article-views {
  display: flex;
  align-items: center;
  gap: 8px;
}

.article-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-btn {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  transition: all 0.3s;
}

.action-btn:hover {
  color: #2d8cf0;
}

.action-btn.active {
  color: #2d8cf0;
}

.article-image {
  width: 100%;
  height: 400px;
  overflow: hidden;
  position: relative;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.5s;
}

.article-image:hover img {
  transform: scale(1.02);
}

.article-content {
  padding: 40px 60px 60px;
  font-size: 16px;
  line-height: 1.8;
  color: #333;
}

.article-content p {
  margin-bottom: 24px;
}

.article-content h2 {
  font-size: 24px;
  font-weight: 600;
  margin: 40px 0 20px;
  color: #222;
}

.article-content h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 30px 0 16px;
  color: #222;
}

.article-content img {
  max-width: 100%;
  border-radius: 8px;
  margin: 20px 0;
}

.article-content a {
  color: #2d8cf0;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.3s;
}

.article-content a:hover {
  border-bottom-color: #2d8cf0;
}

.article-content ul, .article-content ol {
  margin-bottom: 24px;
  padding-left: 24px;
}

.article-content li {
  margin-bottom: 12px;
}

.article-content blockquote {
  border-left: 4px solid #2d8cf0;
  padding: 16px 24px;
  margin: 24px 0;
  background-color: #f9fbff;
  color: #555;
  font-style: italic;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
  text-align: center;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  max-width: 900px;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(45, 140, 240, 0.1);
  border-radius: 50%;
  border-top-color: #2d8cf0;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-container p {
  color: #f56c6c;
  font-size: 18px;
  margin-bottom: 20px;
}

.reload-btn {
  background: #2d8cf0;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.reload-btn:hover {
  background: #2a80d5;
}

.vote-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vote-dialog {
  background: #fff;
  border-radius: 10px;
  padding: 28px 32px 18px 32px;
  min-width: 320px;
  box-shadow: 0 4px 32px rgba(0,0,0,0.13);
}

.vote-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 18px;
  color: #222;
}

.vote-row {
  margin-bottom: 14px;
  font-size: 15px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.vote-row input[type="number"], .vote-row input[type="date"] {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 15px;
  width: 120px;
}

.vote-actions {
  display: flex;
  gap: 18px;
  margin-top: 10px;
}

.vote-submit {
  background: #2d8cf0;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 18px;
  font-size: 15px;
  cursor: pointer;
  transition: background 0.2s;
}

.vote-submit:hover {
  background: #1a73e8;
}

.vote-cancel {
  background: #eee;
  color: #666;
  border: none;
  border-radius: 6px;
  padding: 6px 18px;
  font-size: 15px;
  cursor: pointer;
}

.vote-cancel:hover {
  background: #ddd;
}

.comment-section {
  margin-top: 40px;
  margin-bottom: 40px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.comment-count {
  font-size: 14px;
  color: #888;
  margin-left: 8px;
  font-weight: normal;
}

.comment-area {
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.comment-input {
  width: 100%;
  max-width: 600px;
  padding: 12px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  transition: all 0.3s;
  resize: vertical;
  font-size: 14px;
  color: #606266;
  box-sizing: border-box;
}

.comment-input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64,158,255,.2);
}

.comment-input:disabled {
  background-color: #f5f7fa;
  cursor: not-allowed;
}

.comment-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  width: 100%;
  max-width: 600px;
}

.login-tip {
  color: #f56c6c;
  font-size: 14px;
}

.submit-btn {
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.submit-btn:hover {
  background-color: #66b1ff;
}

.submit-btn:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.no-comment {
  color: #aaa;
  padding: 20px 0;
  text-align: center;
  font-size: 15px;
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.comment-list {
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
  max-width: 600px;
}

.comment-item {
  border-bottom: 1px solid #eee;
  padding: 16px 0;
  transition: background-color 0.2s;
  width: 100%;
}

.comment-item:hover {
  background-color: #f5f8fc;
}

.comment-header {
  font-size: 14px;
  color: #888;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.nickname {
  font-weight: 600;
  color: #2d8cf0;
  margin-right: 16px;
}

.comment-time {
  color: #999;
  font-size: 13px;
}

.comment-content {
  font-size: 15px;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.6;
}

.reply-content {
  font-size: 14px;
  color: #ff9800;
  background: #fffbe6;
  border-radius: 4px;
  padding: 8px 12px;
  margin-top: 8px;
  display: inline-block;
  line-height: 1.5;
  position: relative;
}

.reply-content:before {
  content: "";
  position: absolute;
  top: -6px;
  left: 12px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #fffbe6;
}

.comment-list-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.vote-count {
  margin-left: 10px;
  color: #2d8cf0;
  font-weight: bold;
}
</style>
