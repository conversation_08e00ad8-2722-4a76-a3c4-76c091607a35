/**
 * 浏览历史相关API接口
 */
import request from '../utils/request'

/**
 * 记录用户浏览历史
 * @param {Object} data 浏览记录信息
 * @returns {Promise<Object>} 操作结果
 */
export function recordBrowseHistory(data) {
  return request({
    url: '/api/browse-history',
    method: 'post',
    data
  })
}

/**
 * 获取用户浏览历史列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 浏览历史列表
 */
export function getBrowseHistoryList(params) {
  return request({
    url: '/api/browse-history',
    method: 'get',
    params
  })
}

/**
 * 删除浏览历史记录
 * @param {number} id 历史记录ID
 * @returns {Promise<Object>} 操作结果
 */
export function deleteBrowseHistory(id) {
  return request({
    url: `/api/browse-history/${id}`,
    method: 'delete'
  })
}

/**
 * 清空所有浏览历史
 * @returns {Promise<Object>} 操作结果
 */
export function clearBrowseHistory() {
  return request({
    url: '/api/browse-history/clear',
    method: 'delete'
  })
} 