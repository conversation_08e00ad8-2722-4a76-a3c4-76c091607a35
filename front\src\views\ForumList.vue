<template>
  <div class="forum-container">
    <div class="forum-header">
      <h1 class="forum-title">创业论坛</h1>
    </div>
    
    <div class="tabs">
      <div class="tab" :class="{active: tabType === 0}" @click="changeTab(0)">用户项目</div>
      <div class="tab" :class="{active: tabType === 1}" @click="changeTab(1)">成功案例</div>
    </div>

    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div>加载中...</div>
    </div>
    
    <div v-else-if="forumList.length === 0" class="empty-container">
      <div class="empty-icon">📋</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-tips">您可以尝试切换到其他标签页</div>
    </div>

    <div v-else class="forum-list">
      <div class="forum-card" v-for="item in displayedForumList" :key="item.id" @click="goDetail(item.id)">
        <div class="card-header">
          <div class="card-tag">{{ tabType === 0 ? '用户项目' : '成功案例' }}</div>
          <div class="card-author">作者：{{ item.author }}</div>
        </div>
        
        <h3 class="card-title">{{ item.title }}</h3>
        
        <div class="card-summary">
          {{ item.content ? (item.content.replace(/<[^>]+>/g, '').slice(0, 100) + '...') : '' }}
        </div>
        
        <div class="card-footer">
          <div class="card-time">发布时间: {{ formatTime(item.createTime) }}</div>
          <div class="card-link">查看详情 →</div>
        </div>
      </div>
    </div>
    
    <!-- 分页控制 -->
    <div v-if="forumList.length > 0" class="pagination">
      <button 
        class="pagination-btn" 
        :disabled="currentPage === 1" 
        @click="changePage(currentPage - 1)"
      >
        上一页
      </button>
      <span class="pagination-info">{{ currentPage }} / {{ totalPages }}</span>
      <button 
        class="pagination-btn" 
        :disabled="currentPage === totalPages" 
        @click="changePage(currentPage + 1)"
      >
        下一页
      </button>
    </div>

    <div class="action-bar">
      <button class="publish-btn" @click="showPublishForm = true">发布项目</button>
    </div>

    <!-- 发布项目表单 -->
    <div v-if="showPublishForm" class="publish-form-overlay">
      <div class="publish-form">
        <h2>发布用户项目</h2>
        <div class="form-group">
          <label for="title">标题</label>
          <input
            type="text"
            id="title"
            v-model="publishForm.title"
            placeholder="输入项目标题"
          />
        </div>
        <div class="form-group">
          <label for="author">作者</label>
          <input
            type="text"
            id="author"
            v-model="publishForm.author"
            placeholder="输入作者姓名"
          />
        </div>
        <div class="form-group">
          <label for="content">内容</label>
          <textarea
            id="content"
            v-model="publishForm.content"
            placeholder="请详细描述你的项目"
            rows="8"
          ></textarea>
        </div>
        <div class="form-actions">
          <button class="cancel-btn" @click="showPublishForm = false">取消</button>
          <button class="submit-btn" @click="submitPublish">发布</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import NavBar from '../components/NavBar.vue'
import { fetchForumList, publishUserProject } from '../api/forum'

const router = useRouter()
const tabType = ref(0) // 默认选中"用户项目"
const forumList = ref<any[]>([])
const loading = ref(false)
const showPublishForm = ref(false)
const currentPage = ref(1)
const pageSize = ref(3) // 每页显示3条记录

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(forumList.value.length / pageSize.value) || 1
})

// 计算当前页显示的数据
const displayedForumList = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return forumList.value.slice(start, end)
})

// 切换页码
const changePage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

// 发布表单数据
const publishForm = ref({
  title: '',
  author: '',
  content: ''
})

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
}

// 获取论坛列表
const fetchForums = async () => {
  loading.value = true;
  try {
    const res = await fetchForumList({ type: tabType.value });

    if (res.code === 200 && res.data && res.data.records) {
      forumList.value = res.data.records;
      // 重置页码
      currentPage.value = 1;
    } else {
      forumList.value = [];
      console.error('获取论坛列表失败:', res.message);
    }
  } catch (error) {
    console.error('获取论坛列表出错:', error);
    forumList.value = [];
  } finally {
    loading.value = false;
  }
}

// 切换标签
const changeTab = (type: number) => {
  tabType.value = type;
  fetchForums();
}

// 跳转到详情页
const goDetail = (id: number) => {
  router.push(`/forum/${id}`);
}

// 提交发布
const submitPublish = async () => {
  // 表单验证
  if (!publishForm.value.title || !publishForm.value.author || !publishForm.value.content) {
    alert('请填写完整信息');
    return;
  }

  try {
    const res = await publishUserProject(publishForm.value);

    if (res.code === 200) {
      alert('发布成功');
      // 重置表单
      publishForm.value = { title: '', author: '', content: '' };
      showPublishForm.value = false;
      // 刷新列表
      fetchForums();
    } else {
      alert(`发布失败: ${res.message}`);
    }
  } catch (error) {
    alert('发布失败，请稍后再试');
    console.error('发布项目出错:', error);
  }
}

onMounted(() => {
  fetchForums();
})
</script>

<style scoped>
.forum-container {
  padding: 24px;
  min-height: calc(100vh - 60px);
  background-color: #f8f9fa;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.forum-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.forum-title {
  font-size: 26px;
  font-weight: 600;
  color: #333;
  text-align: left;
  margin: 0;
  position: relative;
}

.forum-title:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -12px;
  width: 60px;
  height: 3px;
  background-color: #1976d2;
  border-radius: 2px;
}

.tabs {
  display: flex;
  margin-bottom: 28px;
  align-items: center;
  border-bottom: 1px solid #eee;
  overflow-x: auto;
  text-align: left;
}

.tab {
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  margin-right: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  color: #555;
  font-weight: 400;
}

.tab.active {
  border-bottom-color: #1976d2;
  color: #1976d2;
  font-weight: 500;
}

.tab:hover:not(.active) {
  border-bottom-color: #bbdefb;
  color: #2196f3;
}

.forum-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
  width: 100%;
}

.forum-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.07);
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.02);
}

.forum-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.card-tag {
  color: #1976d2;
  font-size: 13px;
  background-color: #e3f2fd;
  padding: 3px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.card-author {
  font-size: 13px;
  color: #666;
}

.card-title {
  font-size: 20px;
  color: #333;
  margin-top: 0;
  margin-bottom: 14px;
  font-weight: 600;
  line-height: 1.3;
}

.card-summary {
  font-size: 15px;
  color: #555;
  line-height: 1.6;
  margin-bottom: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  flex-grow: 1;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  padding-top: 14px;
  margin-top: auto;
  align-items: center;
}

.card-time {
  font-size: 13px;
  color: #999;
}

.card-link {
  font-size: 14px;
  color: #1976d2;
  font-weight: 500;
  transition: transform 0.2s;
}

.card-link:hover {
  transform: translateX(3px);
}

/* 分页控制 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
  gap: 12px;
}

.pagination-btn {
  background: #fff;
  color: #1976d2;
  border: 1px solid #1976d2;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #1976d2;
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  border-color: #ccc;
  color: #999;
}

.pagination-info {
  font-size: 14px;
  color: #666;
  padding: 0 10px;
}

.action-bar {
  position: fixed;
  bottom: 28px;
  right: 28px;
  z-index: 10;
}

.publish-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 28px;
  font-size: 15px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
  transition: all 0.3s;
  font-weight: 500;
}

.publish-btn:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(25, 118, 210, 0.4);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  text-align: center;
  min-height: 400px;
  width: 100%;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid rgba(25, 118, 210, 0.1);
  border-top-color: #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 18px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空数据状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  text-align: center;
  min-height: 400px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  width: 100%;
}

.empty-icon {
  font-size: 56px;
  margin-bottom: 20px;
  color: #ccc;
}

.empty-text {
  font-size: 20px;
  color: #666;
  margin-bottom: 10px;
  font-weight: 500;
}

.empty-tips {
  font-size: 15px;
  color: #999;
  margin-bottom: 18px;
}

/* 发布表单样式 */
.publish-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.publish-form {
  background: white;
  border-radius: 16px;
  padding: 32px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.publish-form h2 {
  margin-top: 0;
  margin-bottom: 24px;
  color: #333;
  font-size: 20px;
  font-weight: 600;
  position: relative;
}

.publish-form h2:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -8px;
  width: 40px;
  height: 3px;
  background-color: #1976d2;
  border-radius: 2px;
}

.form-group {
  margin-bottom: 18px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 15px;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  border-color: #1976d2;
  outline: none;
  box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 28px;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.2s;
}

.submit-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #e6e6e6;
}

.submit-btn:hover {
  background: #1565c0;
}

@media (max-width: 768px) {
  .forum-container {
    padding: 16px;
  }
  
  .forum-title {
    font-size: 22px;
  }
  
  .tabs {
    margin-bottom: 20px;
  }
  
  .tab {
    padding: 10px 16px;
    font-size: 14px;
  }
  
  .forum-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .forum-card {
    padding: 18px;
  }
  
  .action-bar {
    bottom: 20px;
    right: 20px;
  }
  
  .publish-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .publish-form {
    width: 95%;
    padding: 24px;
  }
}
</style>
