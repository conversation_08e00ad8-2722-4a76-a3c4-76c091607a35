import request from '@/utils/request'

// ==================== 管理员用户接口 ====================

// 分页查询管理员列表
export function listAdminUsers(params) {
  return request({
    url: '/admin/admins',
    method: 'get',
    params
  })
}

// 根据ID获取管理员详情
export function getAdminUser(id) {
  return request({
    url: `/admin/admins/${id}`,
    method: 'get'
  })
}

// 新增管理员
export function addAdminUser(data) {
  return request({
    url: '/admin/admins',
    method: 'post',
    data
  })
}

// 更新管理员
export function updateAdminUser(id, data) {
  return request({
    url: `/admin/admins/${id}`,
    method: 'put',
    data
  })
}

// 删除管理员
export function deleteAdminUser(id) {
  return request({
    url: `/admin/admins/${id}`,
    method: 'delete'
  })
}

// 更新管理员状态（启用/禁用）
export function updateAdminStatus(id, status) {
  return request({
    url: `/admin/admins/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 重置管理员密码
export function resetAdminPassword(id) {
  return request({
    url: `/admin/admins/${id}/password/reset`,
    method: 'put'
  })
}

// 修改当前管理员密码
export function changePassword(data) {
  return request({
    url: '/admin/users/password',
    method: 'put',
    data
  })
}

// 获取当前登录管理员信息
export function getCurrentAdmin() {
  return request({
    url: '/admin/admins/current',
    method: 'get'
  })
}

// ==================== 前端用户接口 ====================

// 分页查询前端用户列表
export function listAppUsers(params) {
  return request({
    url: '/admin/users/app',
    method: 'get',
    params
  })
}

// 根据ID获取前端用户详情
export function getAppUser(id) {
  return request({
    url: `/admin/users/app/${id}`,
    method: 'get'
  })
}

// 更新前端用户
export function updateAppUser(id, data) {
  return request({
    url: `/admin/users/app/${id}`,
    method: 'put',
    data
  })
}

// 删除前端用户
export function deleteAppUser(id) {
  return request({
    url: `/admin/users/app/${id}`,
    method: 'delete'
  })
}

// 更新前端用户状态（启用/禁用）
export function updateAppUserStatus(id, status) {
  return request({
    url: `/admin/users/app/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 重置前端用户密码
export function resetAppUserPassword(id) {
  return request({
    url: `/admin/users/app/${id}/password/reset`,
    method: 'put'
  })
}

// 登录
export function login(data) {
  return request({
    url: '/admin/auth/login',
    method: 'post',
    data
  })
} 