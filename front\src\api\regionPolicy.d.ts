// regionPolicy.d.ts - API类型声明文件
declare module '../api/regionPolicy' {
  // 地区列表响应
  export interface ApiResponse<T> {
    code: number;
    message?: string;
    data: T;
  }

  // 分页类型
  export interface PageResponse<T> {
    records: T[];
    total: number;
    size: number;
    current: number;
    pages: number;
  }

  // 地区政策类型
  export interface RegionalPolicy {
    id: number;
    regionName: string;
    title: string;
    content: string;
    policyType: string;
    publishTime: string;
    validPeriod: string;
    supportAmount: string | null;
    contactDept: string | null;
    contactPhone: string | null;
    createTime: string;
    updateTime: string;
  }

  // 查询参数类型
  export interface QueryParams {
    pageNum?: number;
    pageSize?: number;
    regionName?: string;
    title?: string;
  }

  /**
   * 获取所有地区分类
   */
  export function fetchRegionList(): Promise<ApiResponse<string[]>>;

  /**
   * 获取地区政策分页列表
   * @param params 查询参数
   */
  export function fetchRegionPolicyList(params?: QueryParams): Promise<ApiResponse<PageResponse<RegionalPolicy>>>;

  /**
   * 获取地区政策详情
   * @param id 政策ID
   */
  export function fetchRegionPolicyDetail(id: string | number): Promise<ApiResponse<RegionalPolicy>>;
} 