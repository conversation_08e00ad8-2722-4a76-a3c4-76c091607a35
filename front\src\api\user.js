/**
 * 用户相关的API接口
 */
import request from '../utils/request'

/**
 * 获取当前用户的个人资料
 * @returns {Promise<Object>} 用户资料对象
 */
export function getUserProfile() {
  return request({
    url: '/api/profile',
    method: 'get',
    transformResponse: [function(data) {
      try {
        // 尝试解析JSON
        const jsonData = JSON.parse(data);
        console.log('用户信息API响应:', jsonData);
        return jsonData;
      } catch (e) {
        // 如果不是JSON，直接返回
        console.log('用户信息API原始响应:', data);
        return data;
      }
    }]
  }).catch(error => {
    console.error('获取用户信息请求错误:', error);
    // 从localStorage获取用户名作为fallback
    const username = localStorage.getItem('username');
    if (username) {
      return { username, fullName: '' };
    }
    throw error;
  });
}

/**
 * 更新用户个人资料
 * @param {Object} data 用户信息对象
 * @returns {Promise<Object>} 更新结果
 */
export function updateUserProfile(data) {
  return request({
    url: '/api/profile',
    method: 'put',
    data
  })
}

/**
 * 获取用户收藏列表
 * @returns {Promise<Object>} 收藏列表数据
 */
export function getUserFavorites() {
  return request({
    url: '/api/profile/favorites',
    method: 'get'
  })
}

/**
 * 获取用户浏览历史
 * @returns {Promise<Object>} 浏览历史数据
 */
export function getBrowseHistory() {
  return request({
    url: '/api/profile/history',
    method: 'get'
  })
}

/**
 * 用户登录
 * @param {Object} data 登录信息对象 {username, password}
 * @returns {Promise<Object>} 登录结果，包含token
 */
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data,
    transformResponse: [function(data) {
      try {
        const jsonData = JSON.parse(data);
        console.log('Login API response:', jsonData);
        return jsonData;
      } catch (e) {
        console.log('Login API raw response:', data);
        return data;
      }
    }]
  })
}

/**
 * 用户注册
 * @param {Object} data 注册信息对象
 * @returns {Promise<Object>} 注册结果
 */
export function register(data) {
  return request({
    url: '/api/auth/register',
    method: 'post',
    data
  })
}

/**
 * 退出登录
 * 清除所有本地存储的用户信息
 */
export function logout() {
  // 清除所有登录相关的本地存储
  localStorage.removeItem('token');
  localStorage.removeItem('username');
  localStorage.removeItem('rememberMe');
  localStorage.removeItem('userInfo');
  
  console.log('用户已完全登出，所有身份信息已清除');
  return Promise.resolve({ code: 200, message: '登出成功' });
}

/**
 * 根据用户名获取用户信息
 * @param {string} username 用户名
 * @returns {Promise<Object>} 用户信息对象
 */
export function fetchUserByUsername(username) {
  return request({
    url: '/api/profile/user/profile',
    method: 'get',
    params: { username }
  });
} 