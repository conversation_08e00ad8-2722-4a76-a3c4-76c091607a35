/**
 * 留言反馈相关API
 */
import request from '../utils/request'

/**
 * 获取留言列表
 * @param {Object} params 查询参数
 * @param {Number} params.page 页码
 * @param {Number} params.limit 每页条数
 * @returns {Promise<Object>} 留言列表数据
 */
export function fetchFeedbackList({ page = 1, limit = 10 } = {}) {
  return request({
    url: '/api/feedback',
    method: 'get',
    params: { page, limit }
  });
}

/**
 * 提交留言
 * @param {Object} data 留言数据
 * @param {String} data.nickname 昵称
 * @param {String} data.content 留言内容
 * @returns {Promise<Object>} 提交结果
 */
export function submitFeedback(data) {
  return request({
    url: '/api/feedback',
    method: 'post',
    data
  });
} 