<template>
  <div class="detail-container">
    <div v-if="forum" class="detail-card">
      <h1>{{ forum.title }}</h1>
      <div class="meta">作者：{{ forum.author }} | 发布时间：{{ formatTime(forum.createTime) }}</div>
      <div class="meta">类型：{{ forum.type === 0 ? '用户项目' : '成功案例' }}</div>
      <div class="content" v-html="forum.content"></div>
    </div>
    <div v-else-if="loading" class="loading">加载中...</div>
    <div v-else class="error">获取内容失败，请稍后再试</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import NavBar from '../components/NavBar.vue'
import { fetchForumDetail } from '../api/forum'

const route = useRoute()
const forum = ref<any>(null)
const loading = ref(true)
const error = ref('')

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
}

const fetchDetail = async () => {
  loading.value = true;
  error.value = '';

  try {
    const res = await fetchForumDetail(route.params.id as string);

    if (res.code === 200) {
      forum.value = res.data;
    } else {
      error.value = res.message || '获取内容失败';
    }
  } catch (err) {
    console.error('获取详情出错:', err);
    error.value = '获取内容出错';
  } finally {
    loading.value = false;
  }
}

onMounted(fetchDetail)
</script>

<style scoped>
.detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}
.detail-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 32px;
}
h1 {
  margin-top: 0;
  color: #333;
  font-size: 24px;
}
.meta {
  color: #888;
  font-size: 14px;
  margin-bottom: 8px;
}
.content {
  font-size: 16px;
  color: #222;
  line-height: 1.8;
  margin-top: 24px;
}
.loading, .error {
  text-align: center;
  color: #888;
  padding: 40px 0;
}
.error {
  color: #ff4757;
}
</style>
