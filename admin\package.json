{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass-embedded": "^1.86.3", "typescript": "~5.7.2", "vite": "^6.2.0"}, "dependencies": {"axios": "^1.9.0", "echarts": "^5.6.0", "element-plus": "^2.9.7", "jwt-decode": "^4.0.0", "vue-router": "^4.5.0"}}