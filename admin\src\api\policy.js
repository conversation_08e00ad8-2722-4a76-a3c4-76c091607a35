import request from '@/utils/request' // 假设 request 工具路径

// 分页查询地区政策列表
export function listPolicies(params) {
  return request({
    url: '/admin/policies',
    method: 'get',
    params // 使用 params 传递查询参数
  })
}

// 根据ID获取地区政策详情
export function getPolicy(id) {
  return request({
    url: `/admin/policies/${id}`,
    method: 'get'
  })
}

// 新增地区政策
export function addPolicy(data) {
  return request({
    url: '/admin/policies',
    method: 'post',
    data // 使用 data 传递请求体
  })
}

// 更新地区政策
export function updatePolicy(id, data) {
  return request({
    url: `/admin/policies/${id}`,
    method: 'put',
    data
  })
}

// 删除地区政策
export function deletePolicy(id) {
  return request({
    url: `/admin/policies/${id}`,
    method: 'delete'
  })
} 