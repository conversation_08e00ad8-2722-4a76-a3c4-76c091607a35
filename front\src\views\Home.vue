<template>
  <div class="home-page">
    <div class="home-banner">
      <div class="banner-content">
        <Swiper
          :modules="[Autoplay]"
          :slides-per-view="1"
          :loop="true"
          :autoplay="{ delay: 3000, disableOnInteraction: false }"
        >
          <SwiperSlide v-for="(item, idx) in banners" :key="idx">
            <img :src="item.img" class="banner-img" />
          </SwiperSlide>
        </Swiper>
      </div>
    </div>
    
    <div class="home-content">
      <div class="policy-recommend">
        <div class="section-header">
          <span class="section-icon">📄</span>
          <span class="section-title">政策推荐</span>
          <button class="more-btn" @click="goToMore('policy')">查看更多</button>
        </div>
        <div class="policy-list">
          <div v-for="item in policyList" :key="item.id" class="policy-card" @click="goPolicyDetail(item.id)">
            <img v-if="item.picture" :src="item.picture" class="policy-img" />
            <div v-else class="policy-img-placeholder">政策</div>
            <div class="policy-content">
              <div class="policy-title">{{ item.title }}</div>
              <div class="policy-desc">{{ item.summary || item.content?.slice(0, 40) || '暂无简介' }}</div>
              <div class="policy-meta">
                <span class="meta-time">{{ formatDate(item.createTime) }}</span>
                <span class="meta-view">👍 {{ item.voteCount || 0 }} 次浏览</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="news-info">
        <div class="section-header">
          <span class="section-icon">ℹ️</span>
          <span class="section-title">资讯信息</span>
          <button class="more-btn" @click="goToMore('news')">查看更多</button>
        </div>
        <div class="news-list">
          <div v-for="item in newsList" :key="item.id" class="news-card" @click="goNewsDetail(item.id)">
            <img v-if="item.picture" :src="item.picture" class="news-img" />
            <div v-else class="news-img-placeholder">资讯</div>
            <div class="news-content">
              <div class="news-title">{{ item.title }}</div>
              <div class="news-desc">{{ item.summary || item.content?.slice(0, 40) || '暂无简介' }}</div>
              <div class="news-meta">
                <span class="meta-time">{{ formatDate(item.createTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { fetchPolicyRecommend, fetchNewsList } from '../api/home'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay } from 'swiper/modules'
import 'swiper/swiper-bundle.css'

const router = useRouter()
const banners = [
  { img: '/banner1.jpg' },
  { img: '/banner2.jpg' },
  { img: '/banner3.jpg' }
]

const policyList = ref<any[]>([])
const newsList = ref<any[]>([])

function formatDate(dateStr: string) {
  if (!dateStr) return '';
  return dateStr.replace('T', ' ').slice(0, 16);
}

function goPolicyDetail(id: number) {
  router.push(`/policy/${id}`)
}

function goNewsDetail(id: number) {
  router.push(`/news/${id}`)
}

function goToMore(type: string) {
  if (type === 'policy') {
    router.push('/policy')
  } else if (type === 'news') {
    router.push('/news')
  }
}

onMounted(async () => {
  const policyRes = await fetchPolicyRecommend()
  if (policyRes.code === 200 && Array.isArray(policyRes.data)) {
    policyList.value = policyRes.data
  }
  const newsRes = await fetchNewsList()
  if (newsRes.code === 200 && Array.isArray(newsRes.data)) {
    newsList.value = newsRes.data
  }
})
</script>

<style scoped>
.home-page {
  width: 100%;
}

.home-banner {
  width: 100%;
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
  background-color: #f0f2f5;
  padding: 15px 0;
}

.banner-content {
  width: 100%;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
}

.banner-img {
  width: 100%;
  height: 450px;
  object-fit: cover;
  display: block;
}

.home-content {
  display: flex;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  gap: 32px;
  margin-bottom: 30px;
}

.policy-recommend, .news-info {
  flex: 1;
  background: #f8fafd;
  border-radius: 12px;
  padding: 24px 18px;
  box-sizing: border-box;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
}

.section-icon {
  font-size: 22px;
  margin-right: 8px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  margin-right: auto;
}

.more-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 4px 16px;
  cursor: pointer;
  font-size: 14px;
}

.policy-card, .news-card {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px 14px;
  margin-bottom: 18px;
  transition: box-shadow 0.2s;
  cursor: pointer;
}

.policy-card:hover, .news-card:hover {
  box-shadow: 0 4px 16px rgba(25, 118, 210, 0.08);
}

.policy-img, .news-img, .policy-img-placeholder, .news-img-placeholder {
  width: 72px;
  height: 72px;
  border-radius: 8px;
  margin-right: 14px;
}

.policy-img, .news-img {
  object-fit: cover;
}

.policy-img-placeholder, .news-img-placeholder {
  background: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
}

.policy-content, .news-content {
  flex: 1;
}

.policy-title, .news-title, 
.policy-desc, .news-desc {
  text-align: left;
}

.policy-title, .news-title {
  font-size: 17px;
  font-weight: 600;
  margin-bottom: 6px;
}

.policy-desc, .news-desc {
  color: #666;
  font-size: 15px;
  margin-bottom: 8px;
}

.policy-meta, .news-meta {
  font-size: 13px;
  color: #999;
  display: flex;
  gap: 18px;
}

@media (max-width: 768px) {
  .home-content {
    flex-direction: column;
    padding: 0 10px;
  }
  
  .banner-img {
    height: 300px;
  }
}
</style>
