<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container" :class="{ 'is-collapse': isCollapse }">
      <div class="logo-container">
        <h1 class="logo-title" :class="{ 'is-collapse': isCollapse }">
          {{ isCollapse ? '管理' : '创业政策管理平台' }}
        </h1>
      </div>
      <!-- 菜单组件 -->
      <el-scrollbar>
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          :unique-opened="true"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          :collapse-transition="false"
          mode="vertical"
        >
          <!-- 首页单独渲染 -->
          <el-menu-item v-if="homeRoute" :index="homeRoute.path" @click="handleMenuClick(homeRoute.path)">
            <el-icon v-if="homeRoute.meta && homeRoute.meta.icon" style="margin-right: 8px">
              <component :is="iconMap[homeRoute.meta.icon]" />
            </el-icon>
            <span>{{ homeRoute.meta.title }}</span>
          </el-menu-item>
          
          <!-- 所有菜单项都直接扁平化渲染为一级菜单项 -->
          <template v-for="menuItem in flattenMenuItems" :key="menuItem.path">
            <el-menu-item :index="menuItem.path" @click="handleMenuClick(menuItem.path)">
              <el-icon v-if="menuItem.icon" style="margin-right: 8px">
                <component :is="menuItem.icon" />
              </el-icon>
              <span>{{ menuItem.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 主区域 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="navbar">
        <div class="left-menu">
          <div class="collapse-btn" @click="toggleSideBar">
            <el-icon><Fold v-if="!isCollapse" /><Expand v-else /></el-icon>
          </div>
          <breadcrumb class="breadcrumb-container" />
        </div>
        <div class="right-menu">
          <div class="avatar-container">
            <el-dropdown trigger="click">
              <div class="avatar-wrapper">
                <el-avatar 
                  :size="32" 
                  :src="userInfo.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" 
                />
                <span class="username">{{ userInfo.name || '管理员' }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="goToChangePassword">
                    <el-icon><Lock /></el-icon>
                    <span>修改密码</span>
                  </el-dropdown-item>
                  <el-dropdown-item divided @click="logout">
                    <el-icon><SwitchButton /></el-icon>
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineComponent, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { Fold, Expand, CaretBottom, User, SwitchButton, Lock } from '@element-plus/icons-vue'
import { routes } from '@/router/index'
import * as ElIcons from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const isCollapse = ref(false)
const toggleSideBar = () => { isCollapse.value = !isCollapse.value }
const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) return meta.activeMenu
  return path
})
// 只过滤掉meta.hidden的路由，并区分首页和其他菜单
const homeRoute = computed(() => {
  // 首页路由通常是path为'/'且children只有一个dashboard
  const home = routes.find(r => r.path === '/' && r.children && r.children.length === 1 && r.children[0].meta?.title)
  return home ? { ...home.children[0], path: `/${home.children[0].path}` } : null
})
// 将所有菜单项扁平化处理，包括子菜单
const flattenMenuItems = computed(() => {
  const result = [];
  
  // 遍历所有路由，找出不是首页和未隐藏的菜单项
  routes.forEach(route => {
    // 跳过首页和隐藏菜单
    if (route.path === '/' || (route.meta && route.meta.hidden)) {
      return;
    }
    
    // 如果有可见子菜单，将所有子菜单直接添加为一级菜单项
    if (route.children && route.children.length > 0) {
      route.children.forEach(child => {
        // 跳过隐藏的子菜单
        if (child.meta && child.meta.hidden) {
          return;
        }
        
        // 构建子菜单的完整路径
        const path = child.path.startsWith('/') 
          ? child.path 
          : `${route.path}/${child.path}`;
          
        // 添加到结果数组
        result.push({
          path: path,
          title: child.meta?.title || child.name || '',
          icon: child.meta?.icon ? iconMap[child.meta.icon] : null
        });
      });
    } 
    // 如果没有子菜单但有meta.title，将其直接添加为一级菜单项
    else if (route.meta && route.meta.title) {
      result.push({
        path: route.path,
        title: route.meta.title,
        icon: route.meta?.icon ? iconMap[route.meta.icon] : null
      });
    }
  });
  
  return result;
});
const userInfo = ref({ name: '管理员', avatar: '' })
const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning'
  }).then(() => {
    localStorage.removeItem('token')
    router.push('/login')
  }).catch(() => {})
}
// 添加跳转到修改密码页面的函数
const goToChangePassword = () => {
  router.push('/profile/change-password')
}
// 图标映射表
const iconMap = {
  'el-icon-s-home': ElIcons.HomeFilled,
  'el-icon-document': ElIcons.Document,
  'el-icon-collection-tag': ElIcons.CollectionTag,
  'el-icon-tickets': ElIcons.Tickets,
  'el-icon-office-building': ElIcons.OfficeBuilding,
  'el-icon-chat-dot-square': ElIcons.ChatDotSquare,
  'el-icon-user': ElIcons.User,
  'el-icon-s-custom': ElIcons.UserFilled,
  'el-icon-user-solid': ElIcons.User,
  'el-icon-s-comment': ElIcons.Comment,
  'el-icon-s-claim': ElIcons.Suitcase,
  'el-icon-setting': ElIcons.Setting,
  'el-icon-s-tools': ElIcons.Tools,
}
// 只渲染有meta.title的菜单项，递归children时也只渲染有meta.title的项
const SidebarItem = defineComponent({
  name: 'SidebarItem',
  props: {
    item: { type: Object, required: true },
    basePath: { type: String, default: '' }
  },
  render() {
    // 获取当前路由的完整路径
    const fullPath = this.basePath
      ? (this.item.path.startsWith('/') ? this.item.path : `${this.basePath}/${this.item.path}`)
      : this.item.path
    
    // 获取图标组件
    const iconComponent = this.item.meta?.icon ? iconMap[this.item.meta.icon] : null
    
    // 判断子路由是否有可见项
    const visibleChildren = (this.item.children || []).filter(child => {
      // 如果子路由没有meta或没有hidden属性，也视为可见
      return !child.meta?.hidden && (child.meta?.title || (!child.children || child.children.length === 0));
    });

    // 创建菜单项的文本内容，优先使用meta.title，其次是name属性
    const menuTitle = this.item.meta?.title || this.item.name || '';
    
    // 渲染条件1：有子路由且有可见项，渲染为子菜单
    if (this.item.children && this.item.children.length > 0 && visibleChildren.length > 0) {
      // 如果有子路由，渲染为SubMenu
      return h('el-sub-menu', { index: fullPath }, {
        title: () => [
          iconComponent ? h('el-icon', { style: { marginRight: '8px' } }, { default: () => h(iconComponent) }) : null,
          h('span', {}, menuTitle)
        ],
        default: () => visibleChildren.map(child => {
          // 获取子路由路径
          const childPath = child.path.startsWith('/') ? child.path : `${fullPath}/${child.path}`
          // 获取子路由图标
          const childIcon = child.meta?.icon ? iconMap[child.meta.icon] : null
          // 获取子路由标题
          const childTitle = child.meta?.title || child.name || '';
          
          // 渲染为MenuItem
          return h('el-menu-item', {
            index: childPath,
            onClick: () => {
              if (childPath && childPath !== '#') {
                router.push(childPath)
              }
            }
          }, [
            childIcon ? h('el-icon', { style: { marginRight: '8px' } }, { default: () => h(childIcon) }) : null,
            h('span', {}, childTitle)
          ])
        })
      })
    }
    
    // 渲染条件2：只有一个路由或者子路由全部隐藏，直接渲染为MenuItem
    // 如果有名称才渲染，无名称则跳过
    if (menuTitle) {
      return h('el-menu-item', {
        index: fullPath,
        onClick: () => {
          if (fullPath && fullPath !== '#') {
            router.push(fullPath)
          }
        }
      }, [
        iconComponent ? h('el-icon', { style: { marginRight: '8px' } }, { default: () => h(iconComponent) }) : null,
        h('span', {}, menuTitle)
      ])
    }
    
    // 如果没有标题，且没有可见的子路由，则不渲染任何内容
    return null;
  }
})

const Breadcrumb = defineComponent({
  name: 'Breadcrumb',
  render() {
    return h('div', { class: 'breadcrumb-container' })
  }
})

defineExpose({ SidebarItem })

// 添加菜单点击处理函数
const handleMenuClick = (path) => {
  console.log('菜单点击路径:', path)
  if (path) {
    router.push(path)
  }
}
</script>

<style>
.el-menu {
  background-color: #304156 !important;
  border-right: none !important;
}
.el-menu-item, .el-sub-menu__title {
  height: 48px !important;
  line-height: 48px !important;
  font-size: 14px !important;
  color: #bfcbd9 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.el-menu-item.is-active, .el-menu-item:hover, .el-sub-menu__title:hover {
  color: #409EFF !important;
  background-color: #263445 !important;
}
.el-menu-item:focus {
  background-color: #263445 !important;
}
</style>

<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  min-width: 210px;
  max-width: 210px;
  height: 100%;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  transition: width 0.28s;
  background-color: #304156;
}

.sidebar-container.is-collapse {
  width: 64px;
  min-width: 64px;
  max-width: 64px;
}

.logo-container {
  height: 60px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #2b3c55;
}

.logo-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.logo-title.is-collapse {
  font-size: 14px;
}

.main-container {
  min-height: 100%;
  transition: margin-left 0.28s;
  margin-left: 210px;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sidebar-container.is-collapse + .main-container {
  margin-left: 64px !important;
}

.navbar {
  height: 60px;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  z-index: 1002;
}

.left-menu {
  display: flex;
  align-items: center;
}

.collapse-btn {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
  transition: color 0.3s;
}

.collapse-btn:hover {
  color: #1890ff;
}

.breadcrumb-container {
  margin-left: 8px;
}

.right-menu {
  display: flex;
  align-items: center;
}

.avatar-container {
  margin-right: 20px;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #333;
}

.app-main {
  min-height: calc(100vh - 60px);
  padding: 20px;
  position: relative;
  overflow: auto;
  background-color: #f0f2f5;
  flex: 1;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* Element Plus 菜单样式覆盖 (使用 :deep) */
:deep(.el-menu) {
  background-color: #304156 !important;
  border-right: none !important;
}
:deep(.el-menu-item), :deep(.el-sub-menu__title) {
  height: 48px !important;
  line-height: 48px !important;
  font-size: 14px !important;
  color: #bfcbd9 !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}
:deep(.el-menu-item .el-icon), :deep(.el-sub-menu__title .el-icon) {
  margin-right: 8px;
}
:deep(.el-menu-item.is-active) {
  color: #409EFF !important;
  background-color: #263445 !important;
}
:deep(.el-menu-item:hover), :deep(.el-sub-menu__title:hover) {
  background-color: #263445 !important;
}
:deep(.el-menu-item:focus) {
  background-color: transparent !important; /* 避免默认 focus 样式*/
}

/* 解决折叠时 tooltip 问题 */
:deep(.el-sub-menu.is-collapse > .el-sub-menu__title .el-sub-menu__icon-arrow) {
  display: none;
}
</style> 