import { createRouter, createWebHistory } from 'vue-router'
import { jwtDecode } from 'jwt-decode'

// 布局组件 - 需要根据实际项目结构调整
const Layout = () => import('@/layout/index.vue') 

// 路由配置
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', hidden: true }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'el-icon-s-home' }
      }
    ]
  },
  {
    path: '/article',
    component: Layout,
    meta: { title: '政策管理', icon: 'el-icon-document' },
    children: [
      {
        path: 'type',
        name: 'ArticleType',
        component: () => import('@/views/article/ArticleTypeManagement.vue'),
        meta: { title: '政策类型管理', icon: 'el-icon-collection-tag' }
      },
      {
        path: 'list',
        name: 'ArticleList',
        component: () => import('@/views/article/ArticleManagement.vue'),
        meta: { title: '政策信息管理', icon: 'el-icon-tickets' }
      }
    ]
  },
  {
    path: '/policy',
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'Policy',
        component: () => import('@/views/policy/PolicyManagement.vue'),
        meta: { title: '地区政策管理', icon: 'el-icon-office-building' }
      }
    ]
  },
  {
    path: '/message',
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'Message',
        component: () => import('@/views/message/MessageManagement.vue'),
        meta: { title: '留言板管理', icon: 'el-icon-chat-dot-square' }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    meta: { title: '用户管理', icon: 'el-icon-user' },
    children: [
      // {
      //   path: 'admin',
      //   name: 'AdminUser',
      //   component: () => import('@/views/user/AdminUserManagement.vue'),
      //   meta: { title: '管理员管理', icon: 'el-icon-s-custom' }
      // }, // 注释掉管理员管理路由
      {
        path: 'app',
        name: 'AppUser',
        component: () => import('@/views/user/AppUserManagement.vue'),
        meta: { title: '用户管理', icon: 'el-icon-user-solid' } // 修改标题为"用户管理"
      }
    ]
  },
  {
    path: '/forum',
    component: Layout,
    // meta: { title: '创业论坛', icon: 'el-icon-s-comment' }, // 如果有父菜单，保留或修改
    children: [
      {
        path: 'list', // 帖子管理保留
        name: 'Forum',
        component: () => import('@/views/forum/ForumManagement.vue'),
        meta: { title: '创业论坛管理', icon: 'el-icon-s-comment' }
      }
      // {
      //  path: 'category', // 假设分类管理的路径是 category
      //  name: 'ForumCategory',
      //  component: () => import('@/views/forum/CategoryManagement.vue'), // 假设分类组件路径
      //  meta: { title: '分类管理' } // 假设分类菜单标题
      // } // 注释或删除此部分
    ]
  },
  {
    path: '/vote',
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'Vote',
        component: () => import('@/views/vote/VoteManagement.vue'),
        meta: { title: '投票信息管理', icon: 'el-icon-s-claim' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    meta: { title: '系统管理', icon: 'el-icon-setting', hidden: true },
    // children: [
    //   {
    //     path: 'config',
    //     name: 'Config',
    //     component: () => import('@/views/system/ConfigManagement.vue'),
    //     meta: { title: '系统配置', icon: 'el-icon-s-tools' }
    //   },
    //   {
    //     path: 'profile',
    //     name: 'Profile',
    //     component: () => import('@/views/system/Profile.vue'),
    //     meta: { title: '个人信息', icon: 'el-icon-s-custom' }
    //   }
    // ] // 移除或注释掉所有 children
  },
  {
    // 新增修改密码路由 (放在 Layout 下)
    path: '/profile',
    component: Layout,
    meta: { hidden: true }, // 不在侧边栏显示
    children: [
      {
        path: 'change-password',
        name: 'ChangePassword',
        component: () => import('@/views/profile/ChangePassword.vue'), // 指向新的组件
        meta: { title: '修改密码' }
      }
    ]
  },
  {
    path: '/news',
    component: Layout,
    children: [
      {
        path: 'list',
        name: 'News',
        component: () => import('@/views/news/NewsList.vue'),
        meta: { title: '资讯信息管理', icon: 'el-icon-news' }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/error/404.vue'),
    meta: { hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory('/policy/admin/dist/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  document.title = to.meta.title ? `${to.meta.title} - 大学生创业政策信息服务平台` : '大学生创业政策信息服务平台'
  // const token = localStorage.getItem('token')
  // console.log('[router][beforeEach] to:', to.path, 'token:', token)
  // if (token) {
  //   try {
  //     const decoded = jwtDecode(token)
  //     console.log('[router][beforeEach] token decoded:', decoded)
  //     if (decoded.exp && decoded.exp * 1000 < Date.now()) {
  //       console.warn('[router][beforeEach] token已过期，清除token')
  //       localStorage.removeItem('token')
  //       if (to.path !== '/login') {
  //         next({ path: '/login' })
  //         return
  //       }
  //     }
  //   } catch (e) {
  //     console.warn('[router][beforeEach] token解析异常，清除token')
  //     localStorage.removeItem('token')
  //     if (to.path !== '/login') {
  //       next({ path: '/login' })
  //       return
  //     }
  //   }
  // }
  // if (to.path === '/login' && token) {
  //   console.log('[router][beforeEach] 已有token，跳转首页')
  //   next({ path: '/' })
  //   return
  // }
  // if (to.path !== '/login' && !token) {
  //   console.warn('[router][beforeEach] 无token，跳转登录页')
  //   next({ path: '/login' })
  //   return
  // }
  next()
})

export default router

export { routes } 
