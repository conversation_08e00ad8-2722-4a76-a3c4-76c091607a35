import { createRouter, createWebHistory } from 'vue-router'
import { isLoggedIn } from '../utils/auth'

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
    meta: { title: '首页' }
  },
  {
    path: '/policy',
    name: 'ArticleList',
    component: () => import('../views/ArticleInfoList.vue'),
    meta: { title: '政策信息' }
  },
  {
    path: '/news',
    name: 'NewsList',
    component: () => import('../views/NewsList.vue'),
    meta: { title: '资讯信息' }
  },
  {
    path: '/region-policy',
    name: 'RegionPolicy',
    component: () => import('../views/RegionPolicyList.vue'),
    meta: { title: '地区政策' }
  },
  {
    path: '/forum',
    name: 'Forum',
    component: () => import('../views/ForumList.vue'),
    meta: { title: '创业论坛' }
  },
  {
    path: '/feedback',
    name: 'Feedback',
    component: () => import('../views/Feedback.vue'),
    meta: { title: '留言反馈' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../views/Profile.vue'),
    meta: { title: '个人中心', requiresAuth: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { title: '登录/注册', hidden: true }
  },
  {
    path: '/policy/:id',
    name: 'ArticleDetail',
    component: () => import('../views/ArticleDetail.vue'),
    meta: { hidden: true }
  },
  {
    path: '/news/:id',
    name: 'NewsDetail',
    component: () => import('../views/NewsDetail.vue'),
    meta: { hidden: true }
  },
  {
    path: '/region-policy/:id',
    name: 'RegionPolicyDetail',
    component: () => import('../views/RegionPolicyDetail.vue'),
    meta: { hidden: true }
  },
  {
    path: '/forum/:id',
    name: 'ForumDetail',
    component: () => import('../views/ForumDetail.vue'),
    meta: { hidden: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title ? `${to.meta.title} - 创业政策系统` : '创业政策系统'
  
  // 检查是否需要登录权限
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查是否已登录
    if (!isLoggedIn()) {
      // 未登录，重定向到登录页面
      next({
        path: '/login',
        query: { redirect: to.fullPath } // 保存原始请求路径，登录后可重定向回来
      })
    } else {
      // 已登录，继续访问
      next()
    }
  } else {
    // 不需要登录权限的页面直接访问
    next()
  }
})

export { routes }
export default router 