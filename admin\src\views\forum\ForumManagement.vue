<template>
  <div class="forum-container">
    <div class="search-box">
      <el-form :inline="true" :model="postsQueryParams" class="demo-form-inline">
        <el-form-item label="标题">
          <el-input v-model="postsQueryParams.title" placeholder="请输入帖子标题" clearable></el-input>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="postsQueryParams.type" placeholder="请选择类型" clearable style="width: 180px;">
            <el-option label="用户项目" :value="0" />
            <el-option label="成功案例" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="operation-container" style="margin-left: 20px;">
        <el-button type="primary" @click="handleAddPost">新增帖子</el-button>
      </div>
    </div>

    <div class="table-box">
      <el-table v-loading="postsLoading" :data="postsList" style="width: 100%" border>
        <el-table-column prop="id" label="ID" width="60"></el-table-column>
        <el-table-column prop="title" label="标题" min-width="150" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'success' : 'info'">
              {{ scope.row.type === 1 ? '成功案例' : '用户项目' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="author" label="作者" width="120"></el-table-column>
        <el-table-column prop="createTime" label="发布时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEditPost(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDeletePost(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="postsQueryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="postsQueryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="postsTotal"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      title="帖子详情"
      v-model="postDialogVisible"
      width="800px"
    >
      <div v-if="currentPost" class="post-detail">
        <h2 class="post-title">
          {{ currentPost.title }}
        </h2>
        <div class="post-meta">
          <span>作者：{{ currentPost.author }}</span>
          <span>类型：{{ currentPost.type === 1 ? '成功案例' : '用户项目' }}</span>
          <span>发表时间：{{ formatDateTime(currentPost.createTime) }}</span>
        </div>
        <div class="post-content" v-html="currentPost.content"></div>

        <div class="post-comments">
          <h3>评论列表</h3>
          <div v-if="commentsList.length === 0" class="no-comments">暂无评论</div>
          <div v-else class="comment-list">
            <div v-for="comment in commentsList" :key="comment.id" class="comment-item">
              <div class="comment-header">
                <span class="comment-author">{{ comment.authorName }}</span>
                <span class="comment-time">{{ comment.createTime }}</span>
                <el-button type="danger" size="small" @click="handleDeleteComment(comment)">删除</el-button>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </div>
          </div>

          <div class="comment-pagination" v-if="commentsTotal > 0">
            <el-pagination
              @current-change="handleCommentsCurrentChange"
              :current-page="commentsQueryParams.page"
              :page-size="commentsQueryParams.limit"
              layout="prev, pager, next"
              :total="commentsTotal"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :title="postFormDialogTitle"
      v-model="postFormDialogVisible"
      width="700px"
      :close-on-click-modal="false"
      @close="resetPostForm"
    >
      <el-form :model="postForm" :rules="postRules" ref="postFormRef" label-width="80px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="postForm.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="postForm.type">
            <el-radio :label="0">用户项目</el-radio>
            <el-radio :label="1">成功案例</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="作者" prop="author">
          <el-input v-model="postForm.author" placeholder="请输入作者"></el-input>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input v-model="postForm.content" type="textarea" :rows="8" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="postFormDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPostForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  listForumPosts,
  getForumPost,
  addForumPost,
  updateForumPost,
  deleteForumPost
} from '@/api/forum'

export default {
  name: 'ForumManagement',
  setup() {
    const postFormRef = ref(null)
    
    const state = reactive({
      postsLoading: false,
      postsList: [],
      postsTotal: 0,
      postsQueryParams: {
        page: 1,
        limit: 10,
        title: '',
        type: '',
      },
      postDialogVisible: false,
      currentPost: null,
      commentsList: [],
      commentsTotal: 0,
      commentsQueryParams: {
        page: 1,
        limit: 10
      },
      postFormDialogVisible: false,
      postFormDialogTitle: '新增帖子',
      postOperationType: 'add',
      postForm: {
        id: undefined,
        title: '',
        content: '',
        author: '',
        type: 1
      },
      postRules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      },
    })

    const getList = async () => {
      state.postsLoading = true
      try {
        const params = {
          page: state.postsQueryParams.page,
          limit: state.postsQueryParams.limit,
          title: state.postsQueryParams.title,
          type: state.postsQueryParams.type
        }
        const res = await listForumPosts(params)
        state.postsList = res.list || []
        state.postsTotal = res.total || 0
      } catch (error) {
        console.error('获取帖子列表失败', error)
        ElMessage.error('获取帖子列表失败')
        state.postsList = []
        state.postsTotal = 0
      } finally {
        state.postsLoading = false
      }
    }

    const handleQuery = () => {
      state.postsQueryParams.page = 1
      getList()
    }

    const resetQuery = () => {
      state.postsQueryParams.title = ''
      state.postsQueryParams.type = ''
      handleQuery()
    }

    const handleSizeChange = (val) => {
      state.postsQueryParams.limit = val
      getList()
    }

    const handleCurrentChange = (val) => {
      state.postsQueryParams.page = val
      getList()
    }

    const handleViewPost = async (row) => {
      state.postDialogVisible = true
      state.currentPost = row
      state.commentsQueryParams.page = 1
      getPostComments()
    }

    const getPostComments = async () => {
      if (!state.currentPost) return
      
      try {
        const res = await listPostComments(state.currentPost.id, state.commentsQueryParams)
        state.commentsList = res.data.list
        state.commentsTotal = res.data.total
      } catch (error) {
        console.error('获取评论列表失败', error)
      }
    }

    const handleCommentsCurrentChange = (val) => {
      state.commentsQueryParams.page = val
      getPostComments()
    }

    const handleDeleteComment = (comment) => {
      ElMessageBox.confirm(
        '确定要删除该评论吗？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deletePostComment(state.currentPost.id, comment.id)
          ElMessage.success('删除成功')
          getPostComments()
        } catch (error) {
          console.error('删除评论失败', error)
        }
      }).catch(() => {})
    }

    const handleDeletePost = (row) => {
      ElMessageBox.confirm(
        `确定要删除帖子"${row.title}"吗？`,
        '提示',
        { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
      ).then(async () => {
        try {
          await deleteForumPost(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          console.error('删除帖子失败', error)
          ElMessage.error('删除帖子失败')
        }
      }).catch(() => {})
    }

    const handleAddPost = () => {
      state.postOperationType = 'add'
      state.postFormDialogTitle = '新增帖子'
      resetPostForm()
      state.postFormDialogVisible = true
    }

    const handleEditPost = (row) => {
      state.postOperationType = 'edit'
      state.postFormDialogTitle = '编辑帖子'
      resetPostForm()
      Object.assign(state.postForm, {
        id: row.id,
        title: row.title,
        content: row.content,
        author: row.author,
        type: row.type
      })
      state.postFormDialogVisible = true
    }

    const resetPostForm = () => {
      if (postFormRef.value) {
        postFormRef.value.resetFields()
      }
      Object.assign(state.postForm, {
        id: undefined,
        title: '',
        content: '',
        author: '',
        type: 1
      })
    }

    const submitPostForm = () => {
      if (!postFormRef.value) return
      postFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const dataToSubmit = { ...state.postForm }
            if (state.postOperationType === 'add') {
              await addForumPost(dataToSubmit)
              ElMessage.success('新增成功')
            } else {
              await updateForumPost(state.postForm.id, dataToSubmit)
              ElMessage.success('更新成功')
            }
            state.postFormDialogVisible = false
            getList()
          } catch (error) {
            console.error('提交帖子失败', error)
            ElMessage.error('提交帖子失败')
          }
        } else {
          ElMessage.warning('请填写必填项')
          return false
        }
      })
    }

    const formatDateTime = (dateTime) => {
      if (!dateTime) return '--'
      const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
      if (isNaN(date.getTime())) return dateTime
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}`
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state),
      postFormRef,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleViewPost,
      handleCommentsCurrentChange,
      handleDeleteComment,
      handleDeletePost,
      handleAddPost,
      handleEditPost,
      resetPostForm,
      submitPostForm,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.forum-container {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
}

.table-box {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.pagination-box {
  margin-top: 15px;
  text-align: right;
}

.post-detail {
  padding: 0 20px;
}

.post-title {
  font-size: 20px;
  margin-bottom: 15px;
  text-align: center;
}

.post-meta {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.post-content {
  min-height: 100px;
  margin-bottom: 30px;
  line-height: 1.8;
}

.post-comments {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.no-comments {
  text-align: center;
  color: #909399;
  margin: 20px 0;
}

.comment-list {
  margin-top: 15px;
}

.comment-item {
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.comment-author {
  font-weight: bold;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-content {
  padding: 5px 0;
  line-height: 1.6;
}

.comment-pagination {
  margin-top: 20px;
  text-align: center;
}
</style> 