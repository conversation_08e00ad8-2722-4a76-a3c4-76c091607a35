<template>
  <div class="policy-management-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <div class="search-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="地区名称">
            <el-input v-model="searchForm.regionName" placeholder="请输入地区名称" clearable />
          </el-form-item>
          <el-form-item label="政策标题">
            <el-input v-model="searchForm.title" placeholder="请输入政策标题" clearable />
          </el-form-item>
          <el-form-item label="政策类型">
            <el-select v-model="searchForm.policyType" placeholder="请选择政策类型" clearable style="width: 220px">
              <el-option label="创业补贴" value="创业补贴" />
              <el-option label="税收优惠" value="税收优惠" />
              <el-option label="融资扶持" value="融资扶持" />
              <el-option label="场地支持" value="场地支持" />
              <el-option label="培训指导" value="培训指导" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-container">
          <el-button type="primary" @click="handleAdd">新增政策</el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="tableLoading"
        :data="policyList"
        border
        style="width: 100%"
        row-key="id"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="regionName" label="地区名称" width="120" />
        <el-table-column prop="title" label="政策标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="policyType" label="政策类型" width="120" />
        <el-table-column prop="publishTime" label="发布时间" width="180" />
        <el-table-column prop="validPeriod" label="有效期" width="150" />
        <el-table-column prop="supportAmount" label="扶持金额" width="120">
          <template #default="scope">
            {{ scope.row.supportAmount ? `¥${scope.row.supportAmount}` : '无' }}
          </template>
        </el-table-column>
        <el-table-column prop="contactDept" label="联系部门" width="150" show-overflow-tooltip />
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button link type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formType === 'add' ? '新增地区政策' : '编辑地区政策'"
      width="700px"
      @close="closeDialog"
    >
      <el-form
        ref="policyFormRef"
        :model="policyForm"
        :rules="rules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item label="地区名称" prop="regionName">
          <el-input v-model="policyForm.regionName" placeholder="请输入地区名称" />
        </el-form-item>
        <el-form-item label="政策标题" prop="title">
          <el-input v-model="policyForm.title" placeholder="请输入政策标题" />
        </el-form-item>
        <el-form-item label="政策类型" prop="policyType">
          <el-select v-model="policyForm.policyType" placeholder="请选择政策类型" style="width: 100%">
            <el-option label="创业补贴" value="创业补贴" />
            <el-option label="税收优惠" value="税收优惠" />
            <el-option label="融资扶持" value="融资扶持" />
            <el-option label="场地支持" value="场地支持" />
            <el-option label="培训指导" value="培训指导" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            v-model="policyForm.publishTime"
            type="datetime"
            placeholder="选择日期时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="有效期" prop="validPeriod">
          <el-input v-model="policyForm.validPeriod" placeholder="例如：自公布之日起五年内有效" />
        </el-form-item>
        <el-form-item label="扶持金额" prop="supportAmount">
          <el-input-number v-model="policyForm.supportAmount" :min="0" :precision="2" :step="1000" style="width: 100%" />
        </el-form-item>
        <el-form-item label="联系部门" prop="contactDept">
          <el-input v-model="policyForm.contactDept" placeholder="请输入联系部门" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="policyForm.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="政策内容" prop="content">
          <el-input
            v-model="policyForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入政策的详细内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="政策详情"
      width="700px"
    >
      <div v-if="currentPolicy" class="policy-detail">
        <h2 class="policy-title">{{ currentPolicy.title }}</h2>
        <div class="policy-meta">
          <span><strong>地区：</strong>{{ currentPolicy.regionName }}</span>
          <span><strong>类型：</strong>{{ currentPolicy.policyType }}</span>
          <span><strong>发布时间：</strong>{{ formatDateTime(currentPolicy.publishTime) }}</span>
          <span><strong>有效期：</strong>{{ currentPolicy.validPeriod }}</span>
          <span v-if="currentPolicy.supportAmount">
            <strong>扶持金额：</strong>¥{{ currentPolicy.supportAmount }}
          </span>
        </div>
        <div class="policy-contact">
          <p><strong>联系部门：</strong>{{ currentPolicy.contactDept }}</p>
          <p><strong>联系电话：</strong>{{ currentPolicy.contactPhone }}</p>
        </div>
        <div class="policy-content">
          <h3>政策内容：</h3>
          <p style="white-space: pre-wrap;">{{ currentPolicy.content }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listPolicies, getPolicy, addPolicy, updatePolicy, deletePolicy } from '@/api/policy'

// 加载状态
const tableLoading = ref(false)

// 表单引用，用于表单校验
const policyFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  regionName: '',
  title: '',
  policyType: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 政策列表数据
const policyList = ref([])

// 对话框控制
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const formType = ref('add') // add: 新增, edit: 编辑

// 当前政策（用于详情查看）
const currentPolicy = ref(null)

// 表单数据
const policyForm = reactive({
  id: undefined,
  regionName: '',
  title: '',
  policyType: '',
  publishTime: null,
  validPeriod: '',
  supportAmount: null,
  contactDept: '',
  contactPhone: '',
  content: ''
})

// 表单校验规则
const rules = {
  regionName: [
    { required: true, message: '请输入地区名称', trigger: 'blur' },
    { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入政策标题', trigger: 'blur' },
    { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
  ],
  policyType: [
    { required: true, message: '请选择政策类型', trigger: 'change' }
  ],
  publishTime: [
    { required: true, message: '请选择发布时间', trigger: 'change' }
  ],
  content: [
    { required: true, message: '请输入政策内容', trigger: 'blur' }
  ]
}

// 初始化加载数据
onMounted(() => {
  fetchPolicyList()
})

// 获取政策列表
const fetchPolicyList = async () => {
  tableLoading.value = true
  try {
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.size,
      ...searchForm
    }

    const res = await listPolicies(params)
    
    // 从响应中解析数据
    // 注意: 如果后端返回是直接的分页对象，使用以下方式获取数据
    policyList.value = res.records || []
    pagination.total = res.total || 0
    
    // 如果后端返回是封装对象，例如 { code: 200, data: { records: [], total: 0 } }
    // policyList.value = res.data.records
    // pagination.total = res.data.total
  } catch (error) {
    console.error('获取政策列表失败:', error)
    ElMessage.error('获取政策列表失败')
  } finally {
    tableLoading.value = false
  }
}

// 查询
const handleSearch = () => {
  pagination.current = 1 // 重置到第一页
  fetchPolicyList()
}

// 重置搜索
const resetSearch = () => {
  // 重置搜索表单
  searchForm.regionName = ''
  searchForm.title = ''
  searchForm.policyType = ''
  
  // 重新查询
  handleSearch()
}

// 处理页码变化
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchPolicyList()
}

// 处理每页条数变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1 // 重置到第一页
  fetchPolicyList()
}

// 查看政策详情
const handleView = async (row) => {
  try {
    const res = await getPolicy(row.id)
    currentPolicy.value = res
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取政策详情失败:', error)
    ElMessage.error('获取政策详情失败')
  }
}

// 新增政策
const handleAdd = () => {
  formType.value = 'add'
  resetForm()
  dialogVisible.value = true
}

// 编辑政策
const handleEdit = async (row) => {
  formType.value = 'edit'
  resetForm()
  
  try {
    const res = await getPolicy(row.id)
    Object.assign(policyForm, res)
    dialogVisible.value = true
  } catch (error) {
    console.error('获取政策详情失败:', error)
    ElMessage.error('获取政策详情失败')
  }
}

// 删除政策
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除政策"${row.title}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await deletePolicy(row.id)
      ElMessage.success('删除成功')
      // 如果删除的是当前页的最后一条数据且不是第一页，则跳转到上一页
      if (policyList.value.length === 1 && pagination.current > 1) {
        pagination.current--
      }
      fetchPolicyList()
    } catch (error) {
      console.error('删除政策失败:', error)
      ElMessage.error('删除政策失败')
    }
  }).catch(() => {
    // 取消删除
  })
}

// 重置表单
const resetForm = () => {
  if (policyFormRef.value) {
    policyFormRef.value.resetFields()
  }
  
  // 手动重置表单数据
  Object.assign(policyForm, {
    id: undefined,
    regionName: '',
    title: '',
    policyType: '',
    publishTime: null,
    validPeriod: '',
    supportAmount: null,
    contactDept: '',
    contactPhone: '',
    content: ''
  })
}

// 关闭对话框
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
}

// 提交表单
const submitForm = async () => {
  if (!policyFormRef.value) return
  
  await policyFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (formType.value === 'add') {
          // 新增
          await addPolicy(policyForm)
          ElMessage.success('新增成功')
        } else {
          // 编辑
          await updatePolicy(policyForm.id, policyForm)
          ElMessage.success('更新成功')
        }
        
        // 关闭对话框并刷新列表
        dialogVisible.value = false
        fetchPolicyList()
      } catch (error) {
        console.error(formType.value === 'add' ? '新增政策失败:' : '更新政策失败:', error)
        ElMessage.error(formType.value === 'add' ? '新增政策失败' : '更新政策失败')
      }
    } else {
      ElMessage.warning('请填写必填项')
      return false
    }
  })
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'
  
  // 如果是字符串类型，先转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime
  
  // 如果转换后不是有效日期
  if (isNaN(date.getTime())) return dateTime
  
  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}`
}
</script>

<style scoped>
.policy-management-container {
  padding: 20px;
}

.search-card,
.table-card {
  margin-bottom: 20px;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.search-form {
  flex: 1;
}

.operation-container {
  margin-left: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 政策详情样式 */
.policy-detail {
  padding: 20px;
}

.policy-title {
  text-align: center;
  margin-bottom: 20px;
}

.policy-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.policy-meta span {
  margin-right: 20px;
  margin-bottom: 10px;
}

.policy-contact {
  margin-bottom: 20px;
  padding: 10px;
  border-left: 3px solid #409eff;
  background-color: #ecf5ff;
}

.policy-content {
  margin-top: 20px;
}

.policy-content h3 {
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}
</style> 