-- ------------------------------
-- Table structure for app_user
-- ------------------------------
CREATE TABLE app_user (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  username VARCHAR(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  full_name <PERSON><PERSON><PERSON><PERSON>(255),
  gender VARCHAR(20),
  avatar VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(50),
  update_time DATETIME
);
INSERT INTO app_user (create_time, username, password, full_name, gender, avatar, email, phone, update_time) VALUES
('2024-04-01 10:00:00', 'alice', '123456', '<PERSON>', '女', 'https://img1.com/avatar1.png', '<EMAIL>', '13800000001', '2024-04-01 12:00:00'),
('2024-04-02 09:30:00', 'bob', 'abcdef', '<PERSON>', '男', 'https://img1.com/avatar2.png', '<EMAIL>', '13800000002', '2024-04-02 10:00:00'),
('2024-04-03 14:20:00', 'charlie', 'pass789', 'Charlie <PERSON>', '男', 'https://img1.com/avatar3.png', '<EMAIL>', '13800000003', '2024-04-03 15:00:00'),
('2024-04-04 08:15:00', 'daisy', 'daisy123', 'Daisy Sun', '女', 'https://img1.com/avatar4.png', '<EMAIL>', '13800000004', '2024-04-04 09:00:00');

-- ------------------------------
-- Table structure for admin_user
-- ------------------------------
CREATE TABLE admin_user (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(255) NOT NULL,
  password VARCHAR(255) NOT NULL,
  role VARCHAR(50),
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO admin_user (username, password, role, create_time, update_time) VALUES
('admin', 'admin123', '超级管理员', '2024-04-01 09:00:00', '2024-04-01 10:00:00'),
('editor', 'editor123', '编辑', '2024-04-02 09:30:00', '2024-04-02 10:00:00'),
('test', 'test123', '测试', '2024-04-03 14:20:00', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for article_type
-- ------------------------------
CREATE TABLE article_type (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  update_time DATETIME,
  type_name VARCHAR(255)
);
INSERT INTO article_type (create_time, update_time, type_name) VALUES
('2024-04-01 10:00:00', '2024-04-01 12:00:00', '通知公告'),
('2024-04-02 09:30:00', '2024-04-02 10:00:00', '创业政策'),
('2024-04-03 14:20:00', '2024-04-03 15:00:00', '经验分享');

-- ------------------------------
-- Table structure for article_info
-- ------------------------------
CREATE TABLE article_info (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  update_time DATETIME,
  title VARCHAR(255),
  article_type VARCHAR(255),
  picture VARCHAR(255),
  summary VARCHAR(255),
  vote_count INT,
  publish_date DATE,
  content TEXT,
  thumbs_up_num INT,
  thumbs_down_num INT,
  last_click_time DATETIME,
  click_num INT
);
INSERT INTO article_info (create_time, update_time, title, article_type, picture, summary, vote_count, publish_date, content, thumbs_up_num, thumbs_down_num, last_click_time, click_num) VALUES
('2024-04-01 10:00:00', '2024-04-01 12:00:00', '大学生创业补贴政策', '创业政策', 'https://img1.com/pic1.png', '补贴政策解读', 10, '2024-04-01', '内容1', 5, 0, '2024-04-01 13:00:00', 100),
('2024-04-02 09:30:00', '2024-04-02 10:00:00', '创业经验分享', '经验分享', 'https://img1.com/pic2.png', '经验交流', 8, '2024-04-02', '内容2', 3, 1, '2024-04-02 11:00:00', 80),
('2024-04-03 14:20:00', '2024-04-03 15:00:00', '最新通知公告', '通知公告', 'https://img1.com/pic3.png', '最新通知', 5, '2024-04-03', '内容3', 2, 0, '2024-04-03 16:00:00', 60);

-- ------------------------------
-- Table structure for article_comment
-- ------------------------------
CREATE TABLE article_comment (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  article_id BIGINT,
  user_id BIGINT,
  nickname VARCHAR(255),
  content TEXT,
  reply_content TEXT,
  update_time DATETIME
);
INSERT INTO article_comment (create_time, article_id, user_id, nickname, content, reply_content, update_time) VALUES
('2024-04-01 11:00:00', 1, 1, 'alice', '很有用的政策！', '感谢您的留言', '2024-04-01 12:00:00'),
('2024-04-02 10:00:00', 2, 2, 'bob', '经验分享很棒', '', '2024-04-02 11:00:00'),
('2024-04-03 15:00:00', 3, 3, 'charlie', '通知收到了', '', '2024-04-03 16:00:00');

-- ------------------------------
-- Table structure for browse_history
-- ------------------------------
CREATE TABLE browse_history (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT,
  username VARCHAR(255),
  table_name VARCHAR(255),
  name VARCHAR(255),
  picture VARCHAR(255),
  type VARCHAR(50),
  inteltime DATETIME,
  refid BIGINT,
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO browse_history (user_id, username, table_name, name, picture, type, inteltime, refid, create_time, update_time) VALUES
(1, 'alice', 'article_info', '大学生创业补贴政策', 'https://img1.com/pic1.png', '政策', '2024-04-01 10:10:00', 1, '2024-04-01 10:10:00', '2024-04-01 12:00:00'),
(2, 'bob', 'article_info', '创业经验分享', 'https://img1.com/pic2.png', '经验', '2024-04-02 09:40:00', 2, '2024-04-02 09:40:00', '2024-04-02 10:00:00'),
(3, 'charlie', 'news_info', '最新通知公告', 'https://img1.com/pic3.png', '通知', '2024-04-03 14:30:00', 3, '2024-04-03 14:30:00', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for config
-- ------------------------------
CREATE TABLE config (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(255),
  value VARCHAR(255),
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO config (name, value, create_time, update_time) VALUES
('site_name', '大学生创业政策信息服务平台', '2024-04-01 09:00:00', '2024-04-01 10:00:00'),
('max_upload_size', '10MB', '2024-04-02 09:30:00', '2024-04-02 10:00:00'),
('theme', 'light', '2024-04-03 14:20:00', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for entrepreneurship_forum
-- ------------------------------
CREATE TABLE entrepreneurship_forum (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(255),
  content TEXT,
  author VARCHAR(255),
  type INT,
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO entrepreneurship_forum (title, content, author, type, create_time, update_time) VALUES
('大学生创业项目交流', '欢迎大家分享创业项目', 'alice', 0, '2024-04-01 10:00:00', '2024-04-01 12:00:00'),
('创业案例分析', '本期案例：某高校创业团队', 'bob', 1, '2024-04-02 09:30:00', '2024-04-02 10:00:00'),
('经验分享', '我的创业心得', 'charlie', 0, '2024-04-03 14:20:00', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for message_board
-- ------------------------------
CREATE TABLE message_board (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  update_time DATETIME,
  user_id BIGINT,
  username VARCHAR(255),
  content TEXT,
  reply_content TEXT
);
INSERT INTO message_board (create_time, update_time, user_id, username, content, reply_content) VALUES
('2024-04-01 10:00:00', '2024-04-01 12:00:00', 1, 'alice', '请问补贴政策如何申请？', '请参考政策栏目'),
('2024-04-02 09:30:00', '2024-04-02 10:00:00', 2, 'bob', '创业经验如何积累？', ''),
('2024-04-03 14:20:00', '2024-04-03 15:00:00', 3, 'charlie', '平台很实用！', '感谢支持');

-- ------------------------------
-- Table structure for news_info
-- ------------------------------
CREATE TABLE news_info (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  update_time DATETIME,
  title VARCHAR(255),
  introduction VARCHAR(255),
  picture VARCHAR(255),
  content TEXT
);
INSERT INTO news_info (create_time, update_time, title, introduction, picture, content) VALUES
('2024-04-01 10:00:00', '2024-04-01 12:00:00', '最新创业新闻', '大学生创业补贴政策发布', 'https://img1.com/news1.png', '内容A'),
('2024-04-02 09:30:00', '2024-04-02 10:00:00', '经验交流会', '创业经验分享', 'https://img1.com/news2.png', '内容B'),
('2024-04-03 14:20:00', '2024-04-03 15:00:00', '通知公告', '平台维护通知', 'https://img1.com/news3.png', '内容C');

-- ------------------------------
-- Table structure for regional_policy
-- ------------------------------
CREATE TABLE regional_policy (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  region_name VARCHAR(255),
  title VARCHAR(255),
  content TEXT,
  policy_type VARCHAR(255),
  publish_time DATETIME,
  valid_period VARCHAR(255),
  support_amount DECIMAL(18,2),
  contact_dept VARCHAR(255),
  contact_phone VARCHAR(50),
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO regional_policy (region_name, title, content, policy_type, publish_time, valid_period, support_amount, contact_dept, contact_phone, create_time, update_time) VALUES
('北京市', '北京大学生创业补贴', '补贴内容A', '补贴', '2024-04-01 10:00:00', '2024-12-31', 10000.00, '市人社局', '010-12345678', '2024-04-01 10:00:00', '2024-04-01 12:00:00'),
('上海市', '上海创业场地支持', '场地内容B', '场地', '2024-04-02 09:30:00', '2024-12-31', 20000.00, '市科委', '021-87654321', '2024-04-02 09:30:00', '2024-04-02 10:00:00'),
('广州市', '广州创业税收优惠', '税收内容C', '税收', '2024-04-03 14:20:00', '2024-12-31', 15000.00, '市财政局', '020-11223344', '2024-04-03 14:20:00', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for user_favorite
-- ------------------------------
CREATE TABLE user_favorite (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  create_time DATETIME,
  user_id BIGINT,
  ref_id BIGINT,
  table_name VARCHAR(255),
  favorite_name VARCHAR(255),
  favorite_picture VARCHAR(255),
  update_time DATETIME
);
INSERT INTO user_favorite (create_time, user_id, ref_id, table_name, favorite_name, favorite_picture, update_time) VALUES
('2024-04-01 10:00:00', 1, 1, 'article_info', '大学生创业补贴政策', 'https://img1.com/pic1.png', '2024-04-01 12:00:00'),
('2024-04-02 09:30:00', 2, 2, 'news_info', '最新创业新闻', 'https://img1.com/news1.png', '2024-04-02 10:00:00'),
('2024-04-03 14:20:00', 3, 3, 'regional_policy', '北京大学生创业补贴', 'https://img1.com/pic2.png', '2024-04-03 15:00:00');

-- ------------------------------
-- Table structure for voting_info
-- ------------------------------
-- 删除旧表（如果存在），避免列冲突
DROP TABLE IF EXISTS `voting_info`;
-- 创建新表结构 (保留表名 voting_info, 使用 toupiaoxinxi 的列定义和注释)
CREATE TABLE `voting_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `addtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `biaoti` varchar(200) DEFAULT NULL COMMENT '标题',
  `wenzhangleixing` varchar(200) DEFAULT NULL COMMENT '政策类型',
  `piaoshu` int NOT NULL DEFAULT '0' COMMENT '票数',
  `toupiaoriqi` date DEFAULT NULL COMMENT '投票日期',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3 COMMENT='投票信息';

-- 插入基础数据
INSERT INTO `voting_info` (`addtime`, `biaoti`, `wenzhangleixing`, `piaoshu`, `toupiaoriqi`) VALUES
('2024-04-01 10:00:00', '你最关注的创业政策', '政策类', 150, '2024-04-01'),
('2024-04-02 09:30:00', '你希望平台新增哪些功能', '功能建议', 205, '2024-04-02'),
('2024-04-03 14:20:00', '你最常用的服务是哪个', '服务使用', 188, '2024-04-03');

-- ------------------------------
-- Table structure for voting_record
-- ------------------------------
CREATE TABLE voting_record (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  voting_id BIGINT,
  user_id BIGINT,
  option VARCHAR(255),
  create_time DATETIME,
  update_time DATETIME
);
INSERT INTO voting_record (voting_id, user_id, option, create_time, update_time) VALUES
(1, 1, '补贴', '2024-04-01 10:10:00', '2024-04-01 12:00:00'),
(2, 2, '论坛', '2024-04-02 09:40:00', '2024-04-02 10:00:00'),
(3, 3, '政策查询', '2024-04-03 14:30:00', '2024-04-03 15:00:00');
