<template>
  <div class="login-container">
    <div class="login-form-container">
      <div class="title-container">
        <h3 class="title">大学生创业政策信息管理平台</h3>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            type="text"
            autocomplete="on"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            autocomplete="on"
            prefix-icon="Lock"
            clearable
          >
            <template #suffix>
              <el-icon class="show-pwd" @click="togglePasswordVisible">
                <component :is="passwordType === 'password' ? 'Hide' : 'View'" />
              </el-icon>
            </template>
          </el-input>
        </el-form-item>

        <el-button
          :loading="loading"
          type="primary"
          class="login-button"
          @click="handleLogin"
        >
          登录
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { User, Lock, View, Hide } from '@element-plus/icons-vue'
import { login } from '@/api/user'

// 路由实例
const router = useRouter()

// 表单引用
const loginFormRef = ref(null)

// 加载状态
const loading = ref(false)

// 密码显示状态
const passwordType = ref('password')

// 登录表单
const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

// 表单校验规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 30, message: '长度在 6 到 30 个字符', trigger: 'blur' }
  ]
}

// 切换密码显示
const togglePasswordVisible = () => {
  passwordType.value = passwordType.value === 'password' ? '' : 'password'
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return
  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 调用后端登录接口
        const res = await login(loginForm)
        localStorage.setItem('token', res.token)
        ElMessage.success('登录成功')
        router.push('/')
      } catch (error) {
        ElMessage.error('登录失败，请检查用户名和密码')
      } finally {
        loading.value = false
      }
    } else {
      ElMessage.warning('请正确填写登录信息')
      return false
    }
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  width: 100%;
  height: 100vh;
  background-color: #2d3a4b;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.login-form-container {
  width: 400px;
  padding: 30px 35px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

.title-container {
  text-align: center;
  margin-bottom: 30px;
  
  .title {
    font-size: 22px;
    color: #333;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(0, 0, 0, 0.03);
    border-radius: 5px;
    margin-bottom: 20px;
    
    ::v-deep(.el-form-item__content) {
      line-height: 40px;
    }
    
    ::v-deep(.el-input) {
      .el-input__wrapper {
        background-color: transparent;
        box-shadow: none;
        padding: 0;
        
        .el-input__prefix-icon {
          margin-left: 10px;
          font-size: 16px;
        }
      }
      
      input {
        height: 40px;
        padding-left: 40px;
        background: transparent;
        border: none;
        border-radius: 0;
        color: #333;
        
        &:-webkit-autofill {
          box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.03) inset !important;
          -webkit-text-fill-color: #333 !important;
        }
      }
    }
  }
  
  .show-pwd {
    cursor: pointer;
    font-size: 16px;
    margin-right: 10px;
    color: #409eff;
  }
  
  .login-button {
    width: 100%;
    height: 42px;
    margin-top: 10px;
  }
}
</style> 