<template>
  <div class="profile-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <div class="card user-card">
          <div class="user-card-header">
            <div class="avatar-wrapper">
              <img :src="userInfo.avatar || defaultAvatar" class="user-avatar" />
              <div class="avatar-upload">
                <el-upload
                  class="avatar-uploader"
                  action="#"
                  :http-request="uploadAvatar"
                  :show-file-list="false"
                  :before-upload="beforeAvatarUpload"
                >
                  <i class="el-icon-camera camera-icon"></i>
                </el-upload>
              </div>
            </div>
            <h3 class="username">{{ userInfo.realName || userInfo.username }}</h3>
            <p class="user-role">管理员</p>
          </div>
          <div class="user-card-body">
            <div class="info-item">
              <i class="el-icon-user"></i>
              <span>{{ userInfo.username }}</span>
            </div>
            <div class="info-item">
              <i class="el-icon-message"></i>
              <span>{{ userInfo.email || '未设置邮箱' }}</span>
            </div>
            <div class="info-item">
              <i class="el-icon-mobile-phone"></i>
              <span>{{ userInfo.phone || '未设置手机号' }}</span>
            </div>
            <div class="info-item">
              <i class="el-icon-date"></i>
              <span>{{ userInfo.createTime || '未知' }}</span>
            </div>
            <div class="info-item">
              <i class="el-icon-location-information"></i>
              <span>{{ userInfo.lastLoginTime || '未知' }}</span>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="16">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本资料" name="basic">
            <div class="card">
              <h3 class="card-title">个人资料</h3>
              <el-form 
                :model="basicForm" 
                :rules="basicRules" 
                ref="basicFormRef" 
                label-width="100px"
                class="profile-form"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="basicForm.username" disabled></el-input>
                </el-form-item>
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="basicForm.realName" placeholder="请输入真实姓名"></el-input>
                </el-form-item>
                <el-form-item label="邮箱" prop="email">
                  <el-input v-model="basicForm.email" placeholder="请输入邮箱"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="basicForm.phone" placeholder="请输入手机号"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitBasicForm">保存</el-button>
                  <el-button @click="resetBasicForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="修改密码" name="password">
            <div class="card">
              <h3 class="card-title">修改密码</h3>
              <el-form 
                :model="passwordForm" 
                :rules="passwordRules" 
                ref="passwordFormRef" 
                label-width="120px"
                class="profile-form"
              >
                <el-form-item label="原密码" prop="oldPassword">
                  <el-input 
                    v-model="passwordForm.oldPassword" 
                    type="password" 
                    placeholder="请输入原密码"
                    show-password
                  ></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="newPassword">
                  <el-input 
                    v-model="passwordForm.newPassword" 
                    type="password" 
                    placeholder="请输入新密码"
                    show-password
                  ></el-input>
                </el-form-item>
                <el-form-item label="确认新密码" prop="confirmPassword">
                  <el-input 
                    v-model="passwordForm.confirmPassword" 
                    type="password" 
                    placeholder="请再次输入新密码"
                    show-password
                  ></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="submitPasswordForm">修改密码</el-button>
                  <el-button @click="resetPasswordForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="登录日志" name="loginLog">
            <div class="card">
              <h3 class="card-title">登录日志</h3>
              <el-table :data="loginLogs" style="width: 100%">
                <el-table-column prop="id" label="序号" width="80"></el-table-column>
                <el-table-column prop="loginTime" label="登录时间" width="180"></el-table-column>
                <el-table-column prop="ipAddress" label="IP地址" width="150"></el-table-column>
                <el-table-column prop="location" label="登录地点"></el-table-column>
                <el-table-column prop="browser" label="浏览器"></el-table-column>
                <el-table-column prop="os" label="操作系统"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                      {{ scope.row.status === 1 ? '成功' : '失败' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination-box">
                <el-pagination
                  @size-change="handleLogSizeChange"
                  @current-change="handleLogCurrentChange"
                  :current-page="logQueryParams.page"
                  :page-sizes="[10, 20, 50, 100]"
                  :page-size="logQueryParams.limit"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="logTotal"
                >
                </el-pagination>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getCurrentAdminInfo,
  updateAdminProfile,
  updateAdminPassword,
  uploadAdminAvatar
} from '@/api/system'
import { getOperationLogs } from '@/api/system'

export default {
  name: 'Profile',
  setup() {
    const basicFormRef = ref(null)
    const passwordFormRef = ref(null)
    
    const state = reactive({
      // 默认头像
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      
      // 当前激活的标签页
      activeTab: 'basic',
      
      // 用户信息
      userInfo: {},
      
      // 基本资料表单
      basicForm: {
        username: '',
        realName: '',
        email: '',
        phone: ''
      },
      
      // 基本资料表单校验规则
      basicRules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        email: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      
      // 密码表单
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      
      // 密码表单校验规则
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不少于6个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value !== state.passwordForm.newPassword) {
                callback(new Error('两次输入的密码不一致'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      
      // 登录日志
      loginLogs: [],
      logTotal: 0,
      logQueryParams: {
        page: 1,
        limit: 10
      }
    })

    // 监听标签页变化
    watch(() => state.activeTab, (newVal) => {
      if (newVal === 'loginLog') {
        getLoginLogs()
      }
    })

    // 获取当前管理员信息
    const getUserInfo = async () => {
      try {
        const res = await getCurrentAdminInfo()
        state.userInfo = res.data
        state.basicForm = {
          username: res.data.username,
          realName: res.data.realName,
          email: res.data.email,
          phone: res.data.phone
        }
      } catch (error) {
        console.error('获取用户信息失败', error)
      }
    }

    // 获取登录日志
    const getLoginLogs = async () => {
      try {
        const res = await getOperationLogs({
          ...state.logQueryParams,
          type: 'login'
        })
        state.loginLogs = res.data.list
        state.logTotal = res.data.total
      } catch (error) {
        console.error('获取登录日志失败', error)
      }
    }

    // 提交基本资料表单
    const submitBasicForm = () => {
      basicFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            await updateAdminProfile(state.basicForm)
            ElMessage.success('个人资料更新成功')
            getUserInfo()
          } catch (error) {
            console.error('更新个人资料失败', error)
          }
        }
      })
    }

    // 重置基本资料表单
    const resetBasicForm = () => {
      basicFormRef.value.resetFields()
      state.basicForm = {
        username: state.userInfo.username,
        realName: state.userInfo.realName,
        email: state.userInfo.email,
        phone: state.userInfo.phone
      }
    }

    // 提交密码表单
    const submitPasswordForm = () => {
      passwordFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            await updateAdminPassword({
              oldPassword: state.passwordForm.oldPassword,
              newPassword: state.passwordForm.newPassword
            })
            ElMessage.success('密码修改成功')
            resetPasswordForm()
          } catch (error) {
            console.error('修改密码失败', error)
          }
        }
      })
    }

    // 重置密码表单
    const resetPasswordForm = () => {
      passwordFormRef.value.resetFields()
      state.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }

    // 上传头像前的校验
    const beforeAvatarUpload = (file) => {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('头像图片只能是JPG或PNG格式!')
        return false
      }
      
      if (!isLt2M) {
        ElMessage.error('头像图片大小不能超过2MB!')
        return false
      }
      
      return true
    }

    // 上传头像
    const uploadAvatar = async (options) => {
      const { file } = options
      
      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file)
      
      try {
        const res = await uploadAdminAvatar(formData)
        state.userInfo.avatar = res.data.url
        ElMessage.success('头像上传成功')
      } catch (error) {
        console.error('头像上传失败', error)
        ElMessage.error('头像上传失败')
      }
    }

    // 日志分页每页大小改变事件
    const handleLogSizeChange = (val) => {
      state.logQueryParams.limit = val
      getLoginLogs()
    }

    // 日志分页当前页改变事件
    const handleLogCurrentChange = (val) => {
      state.logQueryParams.page = val
      getLoginLogs()
    }

    onMounted(() => {
      getUserInfo()
    })

    return {
      ...toRefs(state),
      basicFormRef,
      passwordFormRef,
      getUserInfo,
      submitBasicForm,
      resetBasicForm,
      submitPasswordForm,
      resetPasswordForm,
      beforeAvatarUpload,
      uploadAvatar,
      handleLogSizeChange,
      handleLogCurrentChange
    }
  }
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.card {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.user-card {
  height: 100%;
}

.user-card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
}

.avatar-wrapper {
  position: relative;
  margin-bottom: 15px;
}

.user-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.avatar-upload {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background-color: #409EFF;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.camera-icon {
  color: #fff;
  font-size: 16px;
}

.username {
  font-size: 18px;
  font-weight: bold;
  margin: 10px 0 5px;
}

.user-role {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.user-card-body {
  padding: 20px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.info-item i {
  margin-right: 10px;
  font-size: 18px;
  color: #409EFF;
}

.profile-form {
  max-width: 500px;
}

.pagination-box {
  margin-top: 15px;
  text-align: right;
}
</style> 