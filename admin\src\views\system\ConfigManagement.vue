<template>
  <div class="config-container">
    <div class="config-card">
      <el-form :model="configForm" :rules="rules" ref="configFormRef" label-width="120px">
        <h3 class="config-title">网站基本配置</h3>
        <el-divider></el-divider>
        
        <el-form-item label="网站名称" prop="siteName">
          <el-input v-model="configForm.siteName" placeholder="请输入网站名称"></el-input>
        </el-form-item>
        
        <el-form-item label="网站LOGO" prop="logoUrl">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadLogo"
            :show-file-list="false"
            :before-upload="beforeLogoUpload"
          >
            <img v-if="configForm.logoUrl" :src="configForm.logoUrl" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：200px * 60px，PNG格式</div>
        </el-form-item>
        
        <el-form-item label="网站SEO标题" prop="seoTitle">
          <el-input v-model="configForm.seoTitle" placeholder="请输入SEO标题"></el-input>
        </el-form-item>
        
        <el-form-item label="网站SEO关键词" prop="seoKeywords">
          <el-input v-model="configForm.seoKeywords" placeholder="请输入SEO关键词，以逗号分隔"></el-input>
        </el-form-item>
        
        <el-form-item label="网站SEO描述" prop="seoDescription">
          <el-input 
            v-model="configForm.seoDescription" 
            type="textarea" 
            :rows="3" 
            placeholder="请输入SEO描述"
          ></el-input>
        </el-form-item>
        
        <h3 class="config-title">联系方式</h3>
        <el-divider></el-divider>
        
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="configForm.contactPhone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        
        <el-form-item label="联系邮箱" prop="contactEmail">
          <el-input v-model="configForm.contactEmail" placeholder="请输入联系邮箱"></el-input>
        </el-form-item>
        
        <el-form-item label="联系地址" prop="contactAddress">
          <el-input v-model="configForm.contactAddress" placeholder="请输入联系地址"></el-input>
        </el-form-item>
        
        <h3 class="config-title">系统设置</h3>
        <el-divider></el-divider>
        
        <el-form-item label="是否开启注册" prop="enableRegister">
          <el-switch v-model="configForm.enableRegister"></el-switch>
        </el-form-item>
        
        <el-form-item label="是否需要审核" prop="enableAudit">
          <el-switch v-model="configForm.enableAudit"></el-switch>
        </el-form-item>
        
        <el-form-item label="默认用户头像" prop="defaultAvatarUrl">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadDefaultAvatar"
            :show-file-list="false"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="configForm.defaultAvatarUrl" :src="configForm.defaultAvatarUrl" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">建议尺寸：100px * 100px，JPG或PNG格式</div>
        </el-form-item>
        
        <h3 class="config-title">版权信息</h3>
        <el-divider></el-divider>
        
        <el-form-item label="版权所有" prop="copyrightOwner">
          <el-input v-model="configForm.copyrightOwner" placeholder="请输入版权所有方"></el-input>
        </el-form-item>
        
        <el-form-item label="版权年份" prop="copyrightYear">
          <el-input v-model="configForm.copyrightYear" placeholder="请输入版权年份，如：2020-2023"></el-input>
        </el-form-item>
        
        <el-form-item label="备案号" prop="icp">
          <el-input v-model="configForm.icp" placeholder="请输入ICP备案号"></el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存配置</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getSystemConfig, updateSystemConfig } from '@/api/system'

export default {
  name: 'ConfigManagement',
  components: {
    Plus
  },
  setup() {
    const configFormRef = ref(null)
    
    const state = reactive({
      // 表单对象
      configForm: {
        siteName: '',
        logoUrl: '',
        seoTitle: '',
        seoKeywords: '',
        seoDescription: '',
        contactPhone: '',
        contactEmail: '',
        contactAddress: '',
        enableRegister: true,
        enableAudit: false,
        defaultAvatarUrl: '',
        copyrightOwner: '',
        copyrightYear: '',
        icp: ''
      },
      // 表单校验规则
      rules: {
        siteName: [
          { required: true, message: '请输入网站名称', trigger: 'blur' },
          { max: 50, message: '长度不能超过50个字符', trigger: 'blur' }
        ],
        seoTitle: [
          { max: 100, message: '长度不能超过100个字符', trigger: 'blur' }
        ],
        seoKeywords: [
          { max: 200, message: '长度不能超过200个字符', trigger: 'blur' }
        ],
        seoDescription: [
          { max: 500, message: '长度不能超过500个字符', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    })

    // 获取系统配置
    const getConfig = async () => {
      try {
        const res = await getSystemConfig()
        state.configForm = { ...res.data }
      } catch (error) {
        console.error('获取系统配置失败', error)
      }
    }

    // 上传Logo前的校验
    const beforeLogoUpload = (file) => {
      const isImage = file.type === 'image/png' || file.type === 'image/jpeg'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('Logo只能是JPG或PNG格式！')
        return false
      }
      
      if (!isLt2M) {
        ElMessage.error('Logo大小不能超过2MB！')
        return false
      }
      
      return true
    }

    // 上传头像前的校验
    const beforeAvatarUpload = (file) => {
      const isImage = file.type === 'image/png' || file.type === 'image/jpeg'
      const isLt1M = file.size / 1024 / 1024 < 1
      
      if (!isImage) {
        ElMessage.error('头像只能是JPG或PNG格式！')
        return false
      }
      
      if (!isLt1M) {
        ElMessage.error('头像大小不能超过1MB！')
        return false
      }
      
      return true
    }

    // 上传Logo
    const uploadLogo = async (options) => {
      const { file } = options
      
      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'logo')
      
      try {
        // 这里应该调用真实的上传接口
        const res = await uploadFile(formData)
        state.configForm.logoUrl = res.data.url
        ElMessage.success('Logo上传成功')
      } catch (error) {
        console.error('Logo上传失败', error)
        ElMessage.error('Logo上传失败')
      }
    }

    // 上传默认头像
    const uploadDefaultAvatar = async (options) => {
      const { file } = options
      
      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'avatar')
      
      try {
        // 这里应该调用真实的上传接口
        const res = await uploadFile(formData)
        state.configForm.defaultAvatarUrl = res.data.url
        ElMessage.success('默认头像上传成功')
      } catch (error) {
        console.error('默认头像上传失败', error)
        ElMessage.error('默认头像上传失败')
      }
    }

    // 文件上传（模拟）
    const uploadFile = (formData) => {
      // 这是一个模拟的上传方法，实际开发中应该替换为真实的API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: 200,
            data: {
              url: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
            }
          })
        }, 1000)
      })
    }

    // 提交表单
    const submitForm = () => {
      configFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            await updateSystemConfig(state.configForm)
            ElMessage.success('配置保存成功')
          } catch (error) {
            console.error('保存配置失败', error)
          }
        }
      })
    }

    // 重置表单
    const resetForm = () => {
      getConfig()
    }

    onMounted(() => {
      getConfig()
    })

    return {
      ...toRefs(state),
      configFormRef,
      beforeLogoUpload,
      beforeAvatarUpload,
      uploadLogo,
      uploadDefaultAvatar,
      submitForm,
      resetForm
    }
  }
}
</script>

<style scoped>
.config-container {
  padding: 20px;
}

.config-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.config-title {
  font-size: 18px;
  font-weight: bold;
  margin: 20px 0 10px 0;
  color: #303133;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 