import axios from 'axios';

/**
 * 用户登录
 * @param {string} username - 用户名
 * @param {string} password - 密码
 * @returns {Promise} - 登录结果
 */
export const login = async (username, password) => {
  try {
    const response = await axios.post('/api/auth/login', {
      username,
      password
    });
    
    // 存储token到localStorage
    if (response.data.token) {
      localStorage.setItem('userToken', response.data.token);
    }
    
    return response.data;
  } catch (error) {
    console.error('登录失败:', error);
    throw error;
  }
};

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @returns {Promise} - 注册结果
 */
export const register = async (userData) => {
  try {
    const response = await axios.post('/api/auth/register', userData);
    
    // 如果注册成功并返回了token，存储到localStorage
    if (response.data.token) {
      localStorage.setItem('userToken', response.data.token);
    }
    
    return response.data;
  } catch (error) {
    console.error('注册失败:', error);
    throw error;
  }
};

/**
 * 登出功能
 */
export const logout = () => {
  localStorage.removeItem('userToken');
};

/**
 * 获取当前用户的认证token
 * @returns {string|null} - 认证token或null
 */
export const getToken = () => {
  return localStorage.getItem('userToken');
};

/**
 * 检查用户是否已登录
 * @returns {boolean} - 是否已登录
 */
export const isAuthenticated = () => {
  return !!getToken();
};

/**
 * 为axios请求添加认证头
 * @param {Object} config - axios配置
 * @returns {Object} - 修改后的配置
 */
export const addAuthHeader = (config = {}) => {
  const token = getToken();
  if (token) {
    config.headers = {
      ...config.headers,
      'Authorization': `Bearer ${token}`
    };
  }
  return config;
}; 