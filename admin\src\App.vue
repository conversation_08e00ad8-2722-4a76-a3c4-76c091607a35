<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
// App.vue是根组件，用于包含路由视图
</script>

<style>
body {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html, body, #app {
  height: 100%;
  width: 100%;
}

/* 清除默认样式 */
a {
  text-decoration: none;
}

/* Element UI 全局样式调整 */
.el-card {
  border-radius: 4px;
}

.el-pagination {
  text-align: center;
  margin-top: 20px;
}
</style> 