<template>
  <div class="detail-container">
    <div v-if="detail" class="detail-card">
      <div class="detail-row"><span class="field-label">标题：</span>{{ detail.title }}</div>
      <div class="detail-row" v-if="detail.picture">
        <span class="field-label">封面图片：</span>
        <img :src="detail.picture" class="detail-img" alt="封面" />
      </div>
      <div class="detail-row"><span class="field-label">发布时间：</span>{{ detail.createTime }}</div>
      <div class="detail-row" v-if="detail.author"><span class="field-label">作者：</span>{{ detail.author }}</div>
      <div class="detail-row"><span class="field-label">简介：</span>{{ detail.introduction }}</div>
      <div class="detail-row"><span class="field-label">内容：</span><span v-html="detail.content"></span></div>
    </div>
    <div v-else class="loading">加载中...</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import NavBar from '../components/NavBar.vue'
import { fetchNewsDetail } from '../api/news'
import { recordHistory } from '../utils/history'

const route = useRoute()
const detail = ref<any>(null)
const isLoggedIn = computed(() => !!localStorage.getItem('token'))

onMounted(async () => {
  try {
    const res = await fetchNewsDetail(route.params.id)
    if (res.code === 200) {
      detail.value = res.data
      
      // 记录浏览历史
      if (isLoggedIn.value && detail.value) {
        const historyData = {
          tableName: 'news',
          refid: route.params.id,
          type: '资讯信息',
          name: detail.value.title,
          picture: detail.value.picture || ''
        }
        recordHistory(route, historyData)
      }
    }
  } catch (error) {
    console.error('获取新闻详情失败:', error)
  }
})
</script>

<style scoped>
.detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
}
.detail-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  padding: 32px;
  text-align: left;
}
.detail-row {
  margin-bottom: 14px;
  font-size: 16px;
  color: #333;
  display: flex;
  align-items: flex-start;
  text-align: left;
}
.field-label {
  color: #888;
  min-width: 90px;
  font-weight: 500;
  display: inline-block;
}
.detail-img {
  max-width: 320px;
  max-height: 320px;
  margin-left: 8px;
  border-radius: 8px;
  background: #f5f5f5;
}
.loading {
  text-align: center;
  color: #888;
  padding: 40px 0;
}
</style> 