<template>
  <div class="feedback-container">
    <h1 class="page-title">留言反馈</h1>
    
    <div class="feedback-header">
      <h3 class="list-title">留言列表</h3>
      <button class="feedback-btn" @click="showForm = true">留言</button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div>加载中...</div>
    </div>
    
    <!-- 数据为空状态 -->
    <div v-else-if="feedbackList.length === 0" class="empty-container">
      <div class="empty-icon">💬</div>
      <div class="empty-text">暂无留言</div>
      <div class="empty-tips">快来留下您的宝贵意见吧</div>
      <button class="add-feedback-btn" @click="showForm = true">立即留言</button>
    </div>

    <!-- 留言列表 -->
    <div v-else class="feedback-list">
      <div 
        v-for="item in feedbackList" 
        :key="item.id" 
        class="feedback-card"
      >
        <div class="feedback-user">{{ item.username }}</div>
        <div class="feedback-content">{{ item.content }}</div>
        <div v-if="item.replyContent" class="feedback-reply">
          <span class="reply-label">回复：</span>
          <span class="reply-content">{{ item.replyContent }}</span>
        </div>
        <div class="feedback-time">{{ formatTime(item.createTime) }}</div>
      </div>
    </div>

    <!-- 留言弹窗 -->
    <div v-if="showForm" class="form-overlay">
      <div class="form-panel">
        <div class="form-header">
          <h3>提交留言</h3>
          <button class="close-btn" @click="showForm = false">&times;</button>
        </div>
        
        <div class="form-body">
          <div class="form-group">
            <label for="nickname">昵称</label>
            <input 
              type="text" 
              id="nickname" 
              v-model="form.nickname" 
              placeholder="请输入昵称" 
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label for="content">内容</label>
            <textarea 
              id="content" 
              v-model="form.content" 
              rows="4" 
              placeholder="请输入留言内容" 
              class="form-textarea"
            ></textarea>
          </div>
        </div>
        
        <div class="form-footer">
          <button class="cancel-btn" @click="showForm = false">取消</button>
          <button class="submit-btn" @click="submitFeedback">提交留言</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import NavBar from '../components/NavBar.vue'
import { fetchFeedbackList, submitFeedback as submitFeedbackApi } from '../api/feedback'

const form = ref({ nickname: '', content: '' })
const feedbackList = ref<any[]>([])
const loading = ref(false)
const showForm = ref(false)

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  return date.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-');
}

// 获取留言列表
const fetchFeedbacks = async () => {
  loading.value = true;
  try {
    const res = await fetchFeedbackList();
    if (res.code === 200 && res.data && res.data.records) {
      feedbackList.value = res.data.records;
    } else {
      console.error('获取留言列表失败:', res.message);
    }
  } catch (error) {
    console.error('获取留言列表出错:', error);
  } finally {
    loading.value = false;
  }
}

// 提交留言
const submitFeedback = async () => {
  if (!form.value.nickname || !form.value.content) {
    alert('请填写昵称和留言内容');
    return;
  }
  
  try {
    const res = await submitFeedbackApi(form.value);
    
    if (res.code === 200) {
      alert('留言成功！');
      // 清空内容，保留昵称
      form.value.content = '';
      // 关闭弹窗
      showForm.value = false;
      // 刷新留言列表
      fetchFeedbacks();
    } else {
      alert(`留言失败: ${res.message}`);
    }
  } catch (error) {
    alert('留言失败，请稍后再试');
    console.error('提交留言出错:', error);
  }
}

onMounted(fetchFeedbacks)
</script>

<style scoped>
.feedback-container {
  width: 100%;
  min-height: calc(100vh - 60px); /* 减去导航栏高度 */
  padding: 24px;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
}

.page-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
  text-align: left;
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 0 4px;
}

.list-title {
  font-size: 20px;
  color: #333;
  margin: 0;
  font-weight: 500;
}

.feedback-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

.feedback-btn:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

.feedback-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  width: 100%;
}

.feedback-card {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 20px;
  position: relative;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #eee;
}

.feedback-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
  border-color: #e0e0e0;
}

.feedback-user {
  color: #1976d2;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.feedback-user::before {
  content: '👤';
  margin-right: 8px;
  font-size: 18px;
}

.feedback-content {
  color: #333;
  font-size: 15px;
  line-height: 1.6;
  margin-bottom: 16px;
  flex-grow: 1;
}

.feedback-reply {
  background-color: #f5f8ff;
  padding: 14px;
  border-radius: 8px;
  margin-bottom: 14px;
  font-size: 15px;
  border-left: 3px solid #93bfec;
}

.reply-label {
  color: #ff9800;
  font-weight: 600;
  margin-right: 6px;
}

.reply-content {
  color: #555;
}

.feedback-time {
  font-size: 13px;
  color: #888;
  text-align: right;
  margin-top: auto;
  padding-top: 10px;
  border-top: 1px dashed #eee;
}

/* 留言弹窗样式 */
.form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.form-panel {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slide-up 0.3s ease;
}

@keyframes slide-up {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f8f9fa;
}

.form-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  transition: all 0.2s;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close-btn:hover {
  color: #666;
  background-color: #f0f0f0;
}

.form-body {
  padding: 24px 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 15px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 15px;
  box-sizing: border-box;
  transition: all 0.3s;
  background-color: #fcfcfc;
}

.form-input:focus, .form-textarea:focus {
  border-color: #1976d2;
  outline: none;
  background-color: #fff;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 20px;
  border-top: 1px solid #eee;
  gap: 12px;
  background-color: #f8f9fa;
}

.cancel-btn {
  background: #f0f2f5;
  color: #666;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.3s;
  font-weight: 500;
}

.submit-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 15px;
  transition: all 0.3s;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
}

.cancel-btn:hover {
  background: #e0e3e8;
}

.submit-btn:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
  text-align: center;
  min-height: 300px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
}

.loading-spinner {
  width: 44px;
  height: 44px;
  border: 3px solid rgba(25, 118, 210, 0.1);
  border-top-color: #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 空数据状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 64px 0;
  text-align: center;
  min-height: 300px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
}

.empty-icon {
  font-size: 56px;
  margin-bottom: 16px;
  color: #bdd3ed;
}

.empty-text {
  font-size: 20px;
  color: #444;
  margin-bottom: 10px;
  font-weight: 500;
}

.empty-tips {
  font-size: 15px;
  color: #888;
  margin-bottom: 20px;
}

.add-feedback-btn {
  background: #1976d2;
  color: white;
  border: none;
  padding: 10px 24px;
  border-radius: 6px;
  font-size: 15px;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
  font-weight: 500;
}

.add-feedback-btn:hover {
  background: #1565c0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

@media (max-width: 768px) {
  .feedback-container {
    padding: 16px;
  }
  
  .feedback-list {
    grid-template-columns: 1fr;
  }
  
  .feedback-time {
    position: static;
    text-align: right;
  }
  
  .form-panel {
    width: 95%;
  }
}
</style> 