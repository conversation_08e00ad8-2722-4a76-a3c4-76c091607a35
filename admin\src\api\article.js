import request from '@/utils/request' // 假设 request 工具路径

// 分页查询政策类型列表
export function listArticleTypes(params) {
  return request({
    url: '/admin/article/types',
    method: 'get',
    params // 使用 params 传递查询参数
  })
}

// 根据ID获取政策类型详情
export function getArticleType(id) {
  return request({
    url: `/admin/article/types/${id}`,
    method: 'get'
  })
}

// 新增政策类型
export function addArticleType(data) {
  return request({
    url: '/admin/article/types',
    method: 'post',
    data // 使用 data 传递请求体
  })
}

// 更新政策类型
export function updateArticleType(id, data) {
  return request({
    url: `/admin/article/types/${id}`,
    method: 'put',
    data
  })
}

// 删除政策类型
export function deleteArticleType(id) {
  return request({
    url: `/admin/article/types/${id}`,
    method: 'delete'
  })
}

// 分页查询政策列表
export function listArticles(params) {
  return request({
    url: '/admin/articles',
    method: 'get',
    params
  })
}

// 根据ID获取政策详情
export function getArticle(id) {
  return request({
    url: `/admin/articles/${id}`,
    method: 'get'
  })
}

// 新增政策
export function addArticle(data) {
  return request({
    url: '/admin/articles',
    method: 'post',
    data
  })
}

// 更新政策
export function updateArticle(id, data) {
  return request({
    url: `/admin/articles/${id}`,
    method: 'put',
    data
  })
}

// 删除政策
export function deleteArticle(id) {
  return request({
    url: `/admin/articles/${id}`,
    method: 'delete'
  })
} 
