// user.d.ts - 用户相关API类型声明文件
declare module '../api/user' {
  // API响应基础结构
  export interface ApiResponse<T> {
    code: number;
    message?: string;
    data: T;
  }

  // 用户信息类型
  export interface UserInfo {
    id?: number;
    username: string;
    fullName?: string;
    gender?: string;
    avatar?: string;
    email?: string;
    phone?: string;
    createTime?: string;
    updateTime?: string;
  }

  // 登录请求参数
  export interface LoginParams {
    username: string;
    password: string;
  }

  // 登录响应数据
  export interface LoginResult {
    token: string;
  }

  // 注册请求参数
  export interface RegisterParams {
    username: string;
    password: string;
    fullName?: string;
    gender?: string;
    avatar?: string;
    email?: string;
    phone?: string;
  }

  // 收藏项类型
  export interface UserFavorite {
    id: number;
    userId: number;
    itemId: number;
    itemType: string;
    title: string;
    createTime: string;
  }

  // 浏览历史类型
  export interface BrowseHistory {
    id: number;
    userId: number;
    itemId: number;
    itemType: string;
    title: string;
    createTime: string;
  }

  /**
   * 获取当前用户的个人资料
   */
  export function getUserProfile(): Promise<ApiResponse<UserInfo>>;

  /**
   * 更新用户个人资料
   * @param data 用户信息对象
   */
  export function updateUserProfile(data: Partial<UserInfo>): Promise<ApiResponse<UserInfo>>;

  /**
   * 获取用户收藏列表
   */
  export function getUserFavorites(): Promise<ApiResponse<UserFavorite[]>>;

  /**
   * 获取用户浏览历史
   */
  export function getBrowseHistory(): Promise<ApiResponse<BrowseHistory[]>>;

  /**
   * 用户登录
   * @param data 登录信息对象 {username, password}
   */
  export function login(data: LoginParams): Promise<ApiResponse<LoginResult>>;

  /**
   * 用户注册
   * @param data 注册信息对象
   */
  export function register(data: RegisterParams): Promise<ApiResponse<LoginResult>>;

  /**
   * 退出登录
   * 清除本地存储的token
   */
  export function logout(): Promise<ApiResponse<null>>;
} 