import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus' // 引入Element Plus消息提示

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api', // API的基础URL
  timeout: 15000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('token')
    console.log('[axios][request] token:', token, 'url:', config.url)
    
    // 如果有token则添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  error => {
    // 请求错误处理
    console.error('[axios][request] 请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果您想获取诸如头或状态之类的http信息
   * 请返回 response => response
  */

  /**
   * 通过自定义代码确定请求状态
   * 这里只是一个例子
   * 您也可以通过HTTP状态代码判断状态
   */
  response => {
    // 获取响应数据
    const res = response.data
    console.log('[axios][response] url:', response.config.url, 'status:', response.status, 'data:', res)
    
    // 只在401/403时清除token并跳转，其他情况只弹窗
    if (res.code === 200 || res.code === undefined) {
      return res.data !== undefined ? res.data : res
    }
    if (res.code === 401 || res.code === 403) {
      console.warn('[axios][response] 登录失效，清除token')
      // ElMessageBox.confirm(
      //   '登录状态已过期，请重新登录',
      //   '系统提示',
      //   {
      //     confirmButtonText: '重新登录',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   }
      // ).then(() => {
      //   localStorage.removeItem('token')
      //   window.location.href = '/login'
      // })
    } else {
      ElMessage({
        message: res.message || '系统错误',
        type: 'error',
        duration: 5 * 1000
      })
      // 不再清除token和跳转
    }
    return Promise.reject(new Error(res.message || '系统错误'))
  },
  error => {
    console.error('[axios][response] 响应错误:', error)
    
    // 处理HTTP错误状态
    let message = error.message
    if (error.response) {
      console.warn('[axios][response] HTTP状态:', error.response.status, 'url:', error.response.config.url)
      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          message = '权限不足或登录已失效，请重新登录'
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 404:
          message = '请求地址出错'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${error.response.status}`
      }
    }
    
    ElMessage({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    
    return Promise.reject(error)
  }
)

export default service 