<template>
  <div class="vote-container">
    <div class="search-box">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="标题">
          <el-input v-model="queryParams.title" placeholder="请输入投票标题" clearable></el-input>
        </el-form-item>
        <!-- <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
            <el-option label="未开始" value="0"></el-option>
            <el-option label="进行中" value="1"></el-option>
            <el-option label="已结束" value="2"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-box">
      <div class="tool-bar">
        <el-button type="primary" @click="handleAdd">新增投票</el-button>
      </div>

      <el-table v-loading="loading" :data="voteList" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="title" label="标题" min-width="160"></el-table-column>
        <el-table-column prop="articleType" label="政策类型" width="120"></el-table-column>
        <el-table-column prop="voteCount" label="票数" width="100"></el-table-column>
        <el-table-column prop="votingDate" label="投票日期" width="120">
          <template #default="scope">
            {{ formatDate(scope.row.votingDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-box">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="queryParams.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 添加/编辑投票对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="650px"
      :close-on-click-modal="false"
    >
      <el-form :model="voteForm" :rules="rules" ref="voteFormRef" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input v-model="voteForm.title" placeholder="请输入投票标题"></el-input>
        </el-form-item>
        <el-form-item label="政策类型" prop="articleType">
          <el-input v-model="voteForm.articleType" placeholder="请输入政策类型"></el-input>
        </el-form-item>
        <el-form-item label="票数" prop="voteCount">
          <el-input-number v-model="voteForm.voteCount" :min="0" :max="10000" placeholder="请输入票数"></el-input-number>
        </el-form-item>
        <el-form-item label="投票日期" prop="votingDate">
          <el-date-picker v-model="voteForm.votingDate" type="date" placeholder="请选择投票日期" style="width: 100%;"></el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, toRefs, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElInputNumber, ElDatePicker } from 'element-plus'
import {
  listVotes,
  getVote,
  addVote,
  updateVote,
  deleteVote,
  getVoteOptions
} from '@/api/vote'

export default {
  name: 'VoteManagement',
  setup() {
    const voteFormRef = ref(null)

    const state = reactive({
      // 加载状态
      loading: false,
      // 投票列表
      voteList: [],
      // 总数
      total: 0,
      // 查询参数
      queryParams: {
        page: 1,
        limit: 10,
        title: '',
        // status: ''
      },
      // 操作类型：add-新增，edit-编辑
      operationType: 'add',
      // 对话框标题
      dialogTitle: '新增投票',
      // 对话框可见性
      dialogVisible: false,
      // 表单对象
      voteForm: {
        id: undefined,
        title: '',
        articleType: '',
        voteCount: 0,
        votingDate: ''
      },
      // 表单校验规则
      rules: {
        title: [
          { required: true, message: '请输入投票标题', trigger: 'blur' },
          { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
        ],
        articleType: [
          { required: true, message: '请输入政策类型', trigger: 'blur' }
        ],
        voteCount: [
          { required: true, message: '请输入票数', trigger: ['blur', 'change'] },
          { type: 'integer', min: 0, message: '票数必须是非负整数', trigger: ['blur', 'change'] }
        ],
        votingDate: [
          { required: true, message: '请选择投票日期', trigger: 'change' }
        ]
      }
    })

    // 获取投票列表
    const getList = async () => {
      state.loading = true
      try {
        const res = await listVotes(state.queryParams)
        state.voteList = res.list || []
        state.total = res.total || 0
      } catch (error) {
        console.error('获取投票列表失败', error)
        ElMessage.error(error.message || '获取投票列表失败')
        state.voteList = []
        state.total = 0
      } finally {
        state.loading = false
      }
    }

    // 查询按钮点击事件
    const handleQuery = () => {
      state.queryParams.page = 1
      getList()
    }

    // 重置查询条件
    const resetQuery = () => {
      state.queryParams.title = ''
      // state.queryParams.status = ''
      handleQuery()
    }

    // 每页数量改变事件
    const handleSizeChange = (val) => {
      state.queryParams.limit = val
      getList()
    }

    // 当前页改变事件
    const handleCurrentChange = (val) => {
      state.queryParams.page = val
      getList()
    }

    // 新增投票按钮点击事件
    const handleAdd = () => {
      state.operationType = 'add'
      state.dialogTitle = '新增投票'
      state.voteForm = {
        id: undefined,
        title: '',
        articleType: '',
        voteCount: 0,
        votingDate: ''
      }
      if(voteFormRef.value) {
          voteFormRef.value.resetFields();
      }
      state.dialogVisible = true
    }

    // 编辑投票按钮点击事件
    const handleEdit = async (row) => {
      state.operationType = 'edit'
      state.dialogTitle = '编辑投票信息'
      if (voteFormRef.value) {
          voteFormRef.value.resetFields();
      }
      try {
        state.voteForm = {
          id: row.id,
          title: row.title || '',
          articleType: row.articleType || '',
          voteCount: row.voteCount !== undefined ? row.voteCount : 0,
          votingDate: row.votingDate || ''
        }
        state.dialogVisible = true
      } catch (error) {
        console.error('填充表单失败', error)
        ElMessage.error('加载信息失败');
      }
    }

    // 删除按钮点击事件
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除投票"${row.title}"吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(async () => {
        try {
          await deleteVote(row.id)
          ElMessage.success('删除成功')
          getList()
        } catch (error) {
          console.error('删除失败', error)
        }
      }).catch(() => {})
    }

    // 提交表单
    const submitForm = () => {
      voteFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = { ...state.voteForm }

            if (state.operationType === 'add') {
              await addVote(submitData)
              ElMessage.success('新增成功')
            } else {
              await updateVote(submitData.id, submitData)
              ElMessage.success('更新成功')
            }
            state.dialogVisible = false
            getList()
          } catch (error) {
            console.error('提交表单失败', error)
            ElMessage.error('提交表单失败');
          }
        }
      })
    }

    // 格式化日期
    const formatDate = (date) => {
      if (!date) return ''
      const formattedDate = new Date(date).toLocaleDateString()
      return formattedDate
    }

    // 格式化日期时间
    const formatDateTime = (date) => {
      if (!date) return ''
      const formattedDateTime = new Date(date).toLocaleString()
      return formattedDateTime
    }

    onMounted(() => {
      getList()
    })

    return {
      ...toRefs(state),
      voteFormRef,
      handleQuery,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleAdd,
      handleEdit,
      handleDelete,
      submitForm,
      formatDate,
      formatDateTime
    }
  }
}
</script>

<style scoped>
.vote-container {
  padding: 20px;
}

.search-box {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-box {
  background-color: #fff;
  padding: 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tool-bar {
  margin-bottom: 15px;
}

.pagination-box {
  margin-top: 15px;
  text-align: right;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.option-input {
  margin-right: 10px;
  width: 80%;
}

.statistics-chart {
  width: 100%;
  height: 300px;
  margin: 20px 0;
}

.vote-progress {
  position: relative;
  height: 20px;
  width: 100%;
  background-color: #f5f7fa;
  border-radius: 4px;
  overflow: hidden;
}

.vote-progress-bar {
  height: 100%;
  background-color: #409eff;
}

.vote-progress-text {
  position: absolute;
  top: 0;
  left: 10px;
  height: 100%;
  line-height: 20px;
  color: #fff;
  font-size: 12px;
}
</style>
