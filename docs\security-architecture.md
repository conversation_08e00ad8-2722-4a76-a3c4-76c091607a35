# 微服务安全架构

## 认证授权流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant Service as 业务服务
    participant Redis as 令牌存储
    
    Client->>Gateway: 1. 登录请求
    Gateway->>Auth: 2. 转发登录请求
    Auth->>Auth: 3. 验证凭据
    Auth->>Redis: 4. 生成并存储JWT令牌
    Redis-->>Auth: 5. 确认存储成功
    Auth-->>Gateway: 6. 返回JWT令牌
    Gateway-->>Client: 7. 响应JWT令牌
    
    Client->>Gateway: 8. 业务请求(携带JWT)
    Gateway->>Gateway: 9. 验证JWT有效性
    Gateway->>Service: 10. 转发请求(携带用户信息)
    Service->>Service: 11. 检查权限
    Service-->>Gateway: 12. 返回业务结果
    Gateway-->>Client: 13. 响应业务结果
```

## 网关安全配置

1. **JWT验证过滤器**
   ```java
   @Component
   public class JwtAuthFilter implements GlobalFilter, Ordered {
       
       @Autowired
       private JwtTokenProvider tokenProvider;
       
       @Override
       public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
           ServerHttpRequest request = exchange.getRequest();
           
           // 检查是否需要认证
           if (isOpenApi(request.getPath().toString())) {
               return chain.filter(exchange);
           }
           
           // 获取并验证令牌
           String token = getTokenFromRequest(request);
           if (token == null || !tokenProvider.validateToken(token)) {
               return unauthorizedResponse(exchange);
           }
           
           // 解析用户信息并传递给下游服务
           Claims claims = tokenProvider.getClaimsFromToken(token);
           ServerHttpRequest mutatedRequest = request.mutate()
               .header("X-User-ID", claims.getSubject())
               .header("X-User-Roles", claims.get("roles", String.class))
               .build();
           
           return chain.filter(exchange.mutate().request(mutatedRequest).build());
       }
       
       private boolean isOpenApi(String path) {
           return path.startsWith("/api/auth/") || 
                  path.startsWith("/api/public/") ||
                  path.equals("/api/health");
       }
       
       private String getTokenFromRequest(ServerHttpRequest request) {
           String bearerToken = request.getHeaders().getFirst("Authorization");
           if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
               return bearerToken.substring(7);
           }
           return null;
       }
       
       private Mono<Void> unauthorizedResponse(ServerWebExchange exchange) {
           ServerHttpResponse response = exchange.getResponse();
           response.setStatusCode(HttpStatus.UNAUTHORIZED);
           response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
           
           byte[] bytes = "{\"code\":401,\"message\":\"未授权访问\"}".getBytes(StandardCharsets.UTF_8);
           DataBuffer buffer = response.bufferFactory().wrap(bytes);
           return response.writeWith(Mono.just(buffer));
       }
       
       @Override
       public int getOrder() {
           return -100; // 确保在其他过滤器之前执行
       }
   }
   ```

2. **权限控制**
   ```java
   @Component
   public class Authoriz