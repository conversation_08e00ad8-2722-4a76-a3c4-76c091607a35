<template>
  <div class="navbar-bg">
    <div class="navbar-container">
      <div class="project-title">高校大学生创业政策宣传平台</div>
      
      <div class="nav-content">
        <div class="menu-bar">
          <div v-for="item in menus" :key="item.path" class="menu-item" 
               :class="{ active: route.path === item.path }"
               @click="handleMenuClick(item)">
            {{ item.name }}
          </div>
        </div>

        <div class="user-section">
          <!-- 未登录状态 -->
          <router-link v-if="!isLoggedIn" to="/login" class="login-btn" @click="goToLogin">
            登录 / 注册
          </router-link>
          
          <!-- 已登录状态 -->
          <div v-else class="user-dropdown">
            <div class="user-info" @click="showDropdown = !showDropdown">
              <div class="avatar" v-if="userInfo.avatar">
                <img :src="userInfo.avatar" alt="用户头像" />
              </div>
              <div class="avatar default-avatar" v-else>
                {{ getInitials(userInfo.fullName || userInfo.username) }}
              </div>
              <span class="username">{{ userInfo.fullName || userInfo.username }}</span>
              <span class="dropdown-arrow">▼</span>
            </div>
            
            <div class="dropdown-menu" v-if="showDropdown">
              <router-link to="/profile" class="dropdown-item" @click="goToProfile">
                个人中心
              </router-link>
              <div class="dropdown-item" @click="handleLogout">
                退出登录
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { routes } from '../router'
import { getUserProfile, logout } from '../api/user'
import { recordBrowseHistory } from '../api/history'

const router = useRouter()
const route = useRoute()
const isLoggedIn = ref(false)
const userInfo = ref<any>({})
const showDropdown = ref(false)
const isLoading = ref(true)

// 导航菜单
const menus = routes.filter(r => !r.meta?.hidden && r.meta?.title)
  .map(r => ({
    name: r.meta!.title,
    path: r.path
  }))

// 获取姓名/用户名首字母作为默认头像
const getInitials = (name: string) => {
  if (!name) return '用'
  return name.charAt(0).toUpperCase()
}

// 检查登录状态
const checkLoginStatus = () => {
  const token = localStorage.getItem('token')
  console.log('检查登录状态，token:', token ? '存在' : '不存在')
  isLoggedIn.value = !!token
  return !!token
}

// 获取用户信息
const fetchUserInfo = async () => {
  isLoading.value = true
  if (!checkLoginStatus()) {
    isLoading.value = false
    return
  }
  
  try {
    console.log('获取用户信息...')
    const res = await getUserProfile()
    console.log('用户信息响应:', res)
    
    // 处理不同的响应格式
    if (res) {
      if (res.code === 200 && res.data) {
        userInfo.value = res.data
        isLoggedIn.value = true
        console.log('用户信息获取成功:', userInfo.value)
      } else if (res.id || res.username) {
        // 可能直接返回了用户信息对象
        userInfo.value = res
        isLoggedIn.value = true
        console.log('用户信息获取成功(直接返回):', userInfo.value)
      } else {
        console.error('获取用户信息失败: 格式不正确', res)
        // 如果是401或404错误，清除token并跳转到登录页
        if (res.code === 401 || res.code === 404) {
          console.log('认证失效或用户不存在，清除token并跳转登录页')
          localStorage.removeItem('token')
          isLoggedIn.value = false
          // 如果当前在个人中心页面，跳转到登录页
          if (route.path === '/profile') {
            router.push('/login')
          }
        }
      }
    } else {
      console.error('获取用户信息失败: 无响应')
    }
  } catch (error) {
    console.error('获取用户信息异常:', error)
    // 发生异常时，如果当前在个人中心页面，跳转到登录页
    if (route.path === '/profile') {
      localStorage.removeItem('token')
      isLoggedIn.value = false
      router.push('/login')
    }
  } finally {
    isLoading.value = false
  }
}

// 跳转到登录页面
const goToLogin = () => {
  console.log('点击登录按钮，跳转到登录页面')
  router.push('/login')
}

// 跳转到个人中心页面
const goToProfile = () => {
  console.log('点击个人中心按钮，跳转到个人中心页面')
  showDropdown.value = false
  router.push('/profile')
}

// 处理退出登录
const handleLogout = () => {
  console.log('开始退出登录流程')
  // 调用登出函数，清除所有认证信息
  logout()
  // 更新状态
  isLoggedIn.value = false
  userInfo.value = {}
  showDropdown.value = false
  // 显示提示信息
  alert('您已成功退出登录')
  // 跳转到登录页面而不是首页
  router.push('/login')
}

// 点击其他位置关闭下拉菜单
const handleClickOutside = (event) => {
  const dropdown = document.querySelector('.user-dropdown')
  if (dropdown && !dropdown.contains(event.target) && showDropdown.value) {
    showDropdown.value = false
  }
}

// 如果无法从API获取用户信息，至少显示用户名
const setDefaultUserInfo = () => {
  if (isLoggedIn.value && (!userInfo.value || !userInfo.value.username)) {
    // 从localStorage中获取用户名（如果有）
    const savedUsername = localStorage.getItem('username')
    if (savedUsername) {
      userInfo.value = {
        username: savedUsername,
        fullName: ''
      }
      console.log('使用本地存储的用户名:', savedUsername)
    } else {
      userInfo.value = {
        username: '用户',
        fullName: ''
      }
      console.log('使用默认用户名')
    }
  }
}

// 监听路由变化，重新检查登录状态
watch(() => route.path, (newPath) => {
  console.log('路由变化:', newPath)
  checkLoginStatus()
  if (isLoggedIn.value && (!userInfo.value.username || !userInfo.value.fullName)) {
    fetchUserInfo()
  }
})

// 处理菜单点击事件
const handleMenuClick = (item: any) => {
  // 如果当前已经在该页面，不进行操作
  if (route.path === item.path) return;
  
  // 记录浏览历史
  if (isLoggedIn.value) {
    recordMenuBrowseHistory(item);
  }
  
  // 路由跳转
  router.push(item.path);
}

// 记录菜单浏览历史
const recordMenuBrowseHistory = (item: any) => {
  if (!isLoggedIn.value) return;
  
  // 根据路径确定表名和类型
  let tableName = '';
  let type = '';
  
  if (item.path === '/policy') {
    tableName = 'policy_article';
    type = '政策信息';
  } else if (item.path === '/news') {
    tableName = 'news';
    type = '资讯信息';
  } else if (item.path === '/region-policy') {
    tableName = 'region_policy';
    type = '地区政策';
  } else if (item.path === '/forum') {
    tableName = 'forum';
    type = '创业论坛';
  } else {
    // 其他页面不记录浏览历史
    return;
  }
  
  // 构建浏览历史记录
  const historyData = {
    tableName: tableName,
    type: type,
    name: item.name
  };
  
  // 调用API记录浏览历史
  recordBrowseHistory(historyData)
    .then(res => {
      console.log('菜单浏览历史记录成功:', res);
    })
    .catch(err => {
      console.error('菜单浏览历史记录失败:', err);
    });
}

onMounted(() => {
  console.log('NavBar组件挂载中...')
  checkLoginStatus()
  if (isLoggedIn.value) {
    fetchUserInfo().then(() => {
      // 如果API获取失败，使用默认信息
      setDefaultUserInfo()
    })
  }
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.navbar-bg {
  width: 100vw;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  position: relative;
  z-index: 100;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.project-title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #ff9800;
  padding: 18px 0 8px 0;
  letter-spacing: 2px;
  background: #fff;
  z-index: 2;
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
}

.menu-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: thin;
  flex-grow: 1;
}

.menu-item {
  font-size: 18px;
  color: #333;
  text-decoration: none;
  padding: 4px 12px;
  border-radius: 4px;
  transition: background 0.2s, color 0.2s;
  flex-shrink: 0;
}

.menu-item.active, .menu-item:hover {
  background: #1976d2;
  color: #fff;
}

.user-section {
  margin-left: 20px;
  position: relative;
}

.login-btn {
  display: block;
  padding: 7px 16px;
  background-color: #1976d2;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-size: 15px;
  transition: background-color 0.2s;
}

.login-btn:hover {
  background-color: #1565c0;
}

.user-dropdown {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background 0.2s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
  background-color: #1976d2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 16px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.username {
  font-size: 15px;
  color: #333;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-arrow {
  font-size: 10px;
  color: #666;
  margin-left: 5px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  overflow: hidden;
  width: 120px;
  z-index: 100;
}

.dropdown-item {
  padding: 10px 16px;
  color: #333;
  cursor: pointer;
  font-size: 14px;
  display: block;
  text-decoration: none;
  transition: background 0.2s;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}
</style> 