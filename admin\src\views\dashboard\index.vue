<template>
  <div class="dashboard-container">
    <div class="welcome-section">
      <el-card>
        <div class="welcome-content">
          <h2>欢迎使用大学生创业政策信息服务平台管理后台</h2>
          <p>今天是 {{ currentDate }}，{{ greeting }}</p>
        </div>
      </el-card>
    </div>

    <div class="statistics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistic-card">
            <div class="statistic-item">
              <div class="icon-wrapper bg-blue">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="statistic-info">
                <div class="statistic-title">用户总数</div>
                <div class="statistic-value">{{ stats.userCount }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistic-card">
            <div class="statistic-item">
              <div class="icon-wrapper bg-green">
                <el-icon><Document /></el-icon>
              </div>
              <div class="statistic-info">
                <div class="statistic-title">政策数量</div>
                <div class="statistic-value">{{ stats.policyCount }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistic-card">
            <div class="statistic-item">
              <div class="icon-wrapper bg-orange">
                <el-icon><Reading /></el-icon>
              </div>
              <div class="statistic-info">
                <div class="statistic-title">政策数量</div>
                <div class="statistic-value">{{ stats.articleCount }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistic-card">
            <div class="statistic-item">
              <div class="icon-wrapper bg-purple">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="statistic-info">
                <div class="statistic-title">留言数量</div>
                <div class="statistic-value">{{ stats.messageCount }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="recent-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="recent-card">
            <template #header>
              <div class="card-header">
                <span>最近添加的政策</span>
                <el-button text @click="$router.push('/policy/list')">查看更多</el-button>
              </div>
            </template>
            <div v-if="recentPolicies.length === 0" class="empty-data">暂无数据</div>
            <el-scrollbar height="300px" v-else>
              <div v-for="(item, index) in recentPolicies" :key="index" class="recent-item">
                <div class="item-content">
                  <div class="item-title">{{ item.title }}</div>
                  <div class="item-info">
                    <span>{{ item.regionName }}</span>
                    <span>{{ item.policyType }}</span>
                    <span>{{ formatDateTime(item.createTime) }}</span>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="recent-card">
            <template #header>
              <div class="card-header">
                <span>最近留言</span>
                <el-button text @click="$router.push('/message/list')">查看更多</el-button>
              </div>
            </template>
            <div v-if="recentMessages.length === 0" class="empty-data">暂无数据</div>
            <el-scrollbar height="300px" v-else>
              <div v-for="(item, index) in recentMessages" :key="index" class="recent-item">
                <div class="item-content">
                  <div class="item-title">{{ item.username }}</div>
                  <div class="item-message">{{ item.content }}</div>
                  <div class="item-time">{{ formatDateTime(item.createTime) }}</div>
                </div>
                <div class="item-status">
                  <el-tag size="small" :type="hasReply(item) ? 'success' : 'warning'">
                    {{ hasReply(item) ? '已回复' : '未回复' }}
                  </el-tag>
                </div>
              </div>
            </el-scrollbar>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { UserFilled, Document, Reading, ChatDotRound } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

// 当前日期和问候语
const currentDate = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const day = now.getDate()
  return `${year}年${month}月${day}日`
})

const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) {
    return '凌晨好'
  } else if (hour < 9) {
    return '早上好'
  } else if (hour < 12) {
    return '上午好'
  } else if (hour < 14) {
    return '中午好'
  } else if (hour < 17) {
    return '下午好'
  } else if (hour < 19) {
    return '傍晚好'
  } else {
    return '晚上好'
  }
})

// 统计数据
const stats = ref({
  userCount: 0,
  policyCount: 0,
  articleCount: 0,
  messageCount: 0
})

// 最近添加的政策
const recentPolicies = ref([])

// 最近留言
const recentMessages = ref([])

// 获取统计数据
const fetchStats = async () => {
  try {
    const res = await request({ url: '/admin/dashboard/stats', method: 'get' })
    stats.value = res
  } catch (error) {
    ElMessage.error('获取统计数据失败，请稍后重试')
  }
}

// 获取最近政策
const fetchRecentPolicies = async () => {
  try {
    const res = await request({ url: '/admin/dashboard/recent-policies', method: 'get' })
    recentPolicies.value = res
  } catch (error) {
    ElMessage.error('获取最近政策失败，请稍后重试')
  }
}

// 获取最近留言
const fetchRecentMessages = async () => {
  try {
    const res = await request({ url: '/admin/dashboard/recent-messages', method: 'get' })
    recentMessages.value = res
  } catch (error) {
    ElMessage.error('获取最近留言失败，请稍后重试')
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '--'

  // 如果是字符串类型，先转换为Date对象
  const date = typeof dateTime === 'string' ? new Date(dateTime) : dateTime

  // 如果转换后不是有效日期
  if (isNaN(date.getTime())) return dateTime

  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 判断留言是否已回复
const hasReply = (message) => {
  return message.replyContent && message.replyContent.trim() !== '';
}

// 初始化加载数据
onMounted(() => {
  fetchStats()
  fetchRecentPolicies()
  fetchRecentMessages()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.welcome-section {
  margin-bottom: 20px;
}

.welcome-content {
  text-align: center;
}

.welcome-content h2 {
  margin: 0;
  color: #303133;
}

.welcome-content p {
  margin: 10px 0 0;
  color: #606266;
}

.statistics-section {
  margin-bottom: 20px;
}

.statistic-card {
  border-radius: 4px;
  overflow: hidden;
}

.statistic-item {
  display: flex;
  align-items: center;
  padding: 10px;
}

.icon-wrapper {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.icon-wrapper .el-icon {
  font-size: 30px;
  color: #fff;
}

.bg-blue {
  background-color: #409eff;
}

.bg-green {
  background-color: #67c23a;
}

.bg-orange {
  background-color: #e6a23c;
}

.bg-purple {
  background-color: #8e44ad;
}

.statistic-info {
  flex: 1;
}

.statistic-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 5px;
}

.statistic-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.recent-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-data {
  text-align: center;
  color: #909399;
  padding: 30px 0;
}

.recent-item {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.recent-item:last-child {
  border-bottom: none;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 6px;
  font-weight: 500;
}

.item-info {
  font-size: 12px;
  color: #909399;
}

.item-info span {
  margin-right: 10px;
}

.item-message {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 400px;
}

.item-time {
  font-size: 12px;
  color: #909399;
}

.item-status {
  margin-left: 10px;
}
</style>
