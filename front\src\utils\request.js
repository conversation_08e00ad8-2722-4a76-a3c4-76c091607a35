/**
 * 请求工具模块
 * 封装axios，统一处理请求拦截、响应拦截和错误处理
 */
import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: '', // 根据实际情况配置，使用vite.config.js中的代理，直接使用相对路径
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 统一添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    
    // 输出请求详情，便于调试
    if (config.method === 'get' && config.params) {
      console.log(`请求URL: ${config.url}`, '请求参数:', config.params)
      
      // 特别关注分页参数
      if (config.params.pageNum !== undefined || config.params.current !== undefined) {
        console.log('分页参数:', {
          pageNum: config.params.pageNum,
          current: config.params.current,
          pageSize: config.params.pageSize
        })
      }
    }
    
    return config
  },
  error => {
    // 请求错误处理
    console.error('请求错误：', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 统一处理响应
    const res = response.data
    console.log('原始响应:', res) // 调试日志
    
    // 首先检查是否存在token字段，这是登录接口特有的返回格式
    if (res && res.token) {
      // 对于直接返回 {token: xxx} 格式的响应，直接返回
      return res
    }
    
    // 标准API响应格式处理
    if (res && res.code !== undefined && res.code !== 200) {
      // 处理业务错误码
      if (res.code === 401) {
        // 未授权，可能是token过期
        console.error('登录状态失效，请重新登录')
        // 清除token
        localStorage.removeItem('token')
      }
      
      // 返回业务错误
      const errorMsg = res.message || '请求失败'
      console.error(`业务错误 (${res.code}): ${errorMsg}`)
      return Promise.reject(new Error(errorMsg))
    }
    
    // 返回成功响应
    return res
  },
  error => {
    // 处理HTTP错误状态码
    console.error('响应错误：', error)
    let message = '网络错误，请稍后重试'
    
    if (error.response) {
      // 服务器返回了错误状态码
      switch (error.response.status) {
        case 400:
          message = '请求错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除token
          localStorage.removeItem('token')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求资源不存在'
          break
        case 500:
          message = '服务器错误'
          break
        default:
          message = `未知错误(${error.response.status})`
      }
    }
    
    console.error(message)
    return Promise.reject(error)
  }
)

// 封装请求方法
const request = config => {
  return service(config)
}

export default request 