<template>
  <div class="detail-container">
    <div class="action-bar">
      <button class="back-button" @click="goBack">
        <span class="icon">←</span> 返回列表
      </button>
    </div>
    
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div>加载政策详情中...</div>
    </div>
    
    <div v-else-if="error" class="error-container">
      <div class="error-icon">!</div>
      <div class="error-message">{{ error }}</div>
      <button class="retry-button" @click="fetchDetail">重试</button>
    </div>
    
    <div v-else-if="policy" class="detail-card">
      <h1>{{ policy.title }}</h1>
      <div class="meta-section">
        <div class="meta"><span class="meta-label">地区：</span>{{ policy.regionName }}</div>
        <div class="meta"><span class="meta-label">类型：</span>{{ policy.policyType }}</div>
        <div class="meta"><span class="meta-label">有效期：</span>{{ policy.validPeriod || '无' }}</div>
        <div class="meta"><span class="meta-label">发布时间：</span>{{ formatDate(policy.publishTime) }}</div>
        <div class="meta"><span class="meta-label">支持金额：</span>{{ policy.supportAmount || '无' }}</div>
        <div class="meta"><span class="meta-label">联系电话：</span>{{ policy.contactPhone || '无' }}</div>
        <div class="meta"><span class="meta-label">联系部门：</span>{{ policy.contactDept || '无' }}</div>
      </div>
      
      <div class="content-section">
        <h2>政策详情</h2>
        <div class="content" v-html="policy.content"></div>
      </div>
    </div>
    
    <div v-else class="empty-state">
      未找到政策详情
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { fetchRegionPolicyDetail } from '../api/regionPolicy'

const route = useRoute()
const router = useRouter()
const policy = ref<any>(null)
const loading = ref(true)
const error = ref('')

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '无'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '-')
  } catch (e) {
    return dateString
  }
}

const fetchDetail = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const res = await fetchRegionPolicyDetail(route.params.id as string)
    if (res.code === 200 && res.data) {
      policy.value = res.data
      console.log('政策详情:', policy.value)
    } else {
      error.value = res.message || '获取政策详情失败'
      console.error('获取政策详情失败:', res.message)
    }
  } catch (err: any) {
    error.value = err?.message || '获取政策详情异常'
    console.error('获取政策详情异常:', err)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

onMounted(fetchDetail)
</script>

<style scoped>
.detail-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: none;
  border-radius: 4px;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.back-button:hover {
  background-color: #e0e0e0;
}

.back-button .icon {
  font-size: 18px;
}

.detail-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
  padding: 32px;
}

.detail-card h1 {
  margin: 0 0 24px 0;
  font-size: 24px;
  color: #222;
  line-height: 1.4;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.meta-section {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px 24px;
  margin-bottom: 24px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
}

.meta {
  color: #444;
  font-size: 14px;
  line-height: 1.6;
}

.meta-label {
  color: #888;
  font-weight: 500;
  min-width: 70px;
  display: inline-block;
}

.content-section {
  margin-top: 24px;
}

.content-section h2 {
  font-size: 18px;
  color: #222;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.content {
  font-size: 16px;
  color: #333;
  line-height: 1.8;
}

.loading-container {
  text-align: center;
  color: #888;
  padding: 60px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  color: #d32f2f;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.error-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #ffebee;
  color: #d32f2f;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.error-message {
  font-size: 16px;
  max-width: 400px;
}

.retry-button {
  margin-top: 8px;
  padding: 8px 16px;
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #d32f2f;
}

.empty-state {
  text-align: center;
  color: #888;
  padding: 60px 0;
  font-size: 16px;
}
</style> 